<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;
use App\Models\Tenants\ChsoneInvestmentsMaster;
use App\Models\Tenants\ChsoneAssetsCategory;
use App\Models\Tenants\ChsoneTaxCategory;
use App\Models\Tenants\ChsoneTaxClass;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\TaxExemption;
use App\Models\Tenants\ChsoneBankRecoMaster;
use App\Models\Tenants\ChsoneAccountsMaster;


class EditBankAccountDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:EditBankAccount {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Post New Ledger Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $postedValues = request()->all();
        $account_id = $id ?? '';
        $mode = $postedValues["mode"] ?? '';
        $soc_id = $this->input['company_id'];
        $account_id = $postedValues["account_id"] ?? '';
        $bank_name = $postedValues["bank_name"] ?? '';
        $account_number = $postedValues["account_number"] ?? '';
        $bank_address = $postedValues["bank_address"] ?? '';
        $bank_city = $postedValues["bank_city"] ?? '';
        $account_name = $postedValues["account_name"] ?? '';
        $branch = $postedValues["branch"] ?? '';
        $bank_ifsc = $postedValues["bank_ifsc"] ?? '';
        $default_account = isset($postedValues["default_bank"]) ? 1 : 0;
        $default_bank_for_incidental = isset($postedValues["default_bank_for_incidental"]) ? 1 : 0;
        $default_bank_for_nonmember = isset($postedValues["default_bank_for_nonmember"]) ? 1 : 0;
        $opening_balance = $postedValues["opening_balance"] ?? '';
        $newBankAccountObj = ChsoneAccountsMaster::where('ledger_account_id', $account_id)
            ->where('soc_id', $soc_id)
            ->first();
        $newBankAccountObj->soc_id = $soc_id;
        $newBankAccountObj->bank_name = $bank_name;
        $newBankAccountObj->account_number = $account_number;
        $newBankAccountObj->bank_address = $bank_address;
        $newBankAccountObj->bank_city = $bank_city;
        $newBankAccountObj->account_name = $account_name;
        $newBankAccountObj->branch = $branch;
        $newBankAccountObj->bank_ifsc = $bank_ifsc;
        $newBankAccountObj->default_account = $default_account;
        $newBankAccountObj->default_bank_for_incidental = $default_bank_for_incidental;
        $newBankAccountObj->default_bank_for_nonmember = $default_bank_for_nonmember;
        //$newBankAccountObj->opening_balance = $opening_balance;
        $newBankAccountObj->status = 1;
        $newBankAccountObj->created_by = $this->input['user_id'];
        $newBankAccountObj->added_on = now();
        $newBankAccountObj->modified_on = now();
        $newBankAccountObj->save();
        $message = "Bank Account has been updated successfully";
        $status = 'success';
        $statusCode = 200;
        return response()->json($message, $statusCode);
    }
}
