<?php

namespace App\Console\Commands\Workflows\Account\FinancialStatements;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class CashflowWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:cashFlow {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cash Flow Statement';

    /**
     * Execute the console command.
     */
    
    protected $schema = [
        "table" => [
            'tableDirection' => 'row',
            "tableTitle" => "Cash Flow Statement",
            "fields" => [
                "*"
            ],
            "tableDirection" => 'row',
            "extraFilters" => [
                "acc_date" => [
                    "title" => "Accounting Date",
                    "type" => "account_date"
                ]
            ],
            "columns" => [
                [
                    [
                        "title" => "Inflow",
                        "key" => "ledger_account_name",
                        "footer" => "Total"
                    ],
                    [
                        "title" => "Amount",
                        "key" => "amount",
                        "type" => "number",
                        "aggregation" => true
                    ],
                ],
                [
                    [
                        "title" => "Outflow",
                        "key" => "ledger_account_name",
                        "footer" => "Total"
                    ],
                    [
                        "title" => "Amount",
                        "key" => "amount",
                        "type" => "number",
                        "aggregation" => true
                    ],
                ]
            ]
        ]
    ];

     public function apply()
    {
        $data = $this->action('action:cashFlow', $this->pointer, $this->request);
        $this->data = $data;
        $this->meta['schema'] = $this->schema;
    }
}