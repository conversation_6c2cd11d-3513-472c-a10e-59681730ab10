<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class DownloadReceiptReportWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:downloadReceiptReport {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Download the receipt Report';

    protected $headings = [
        'Receipt Date',
        'Receipt No',
        'Receipt From',
        'Mode',
        'Paid By',
        'Payment Of',
        'Payment Reference',
        'Building / Unit',
        'Paid Amount(TDS)',
        'Write Off',
        'Status',
    ];
    /**
     * Execute the console command.
     */
    public function apply()
    {
        $type = $this->input['type'];

        if (!$type || $type == '' || $type == ':type') {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        } else {
            $MemberReceiptList = $this->action('datasource:ReceiptListReport', $this->pointer, $this->request);
            $detailRows = $MemberReceiptList[0] ?? [];
            $summaryTotals = $MemberReceiptList[1][0] ?? [];
            $outputData = [];
            foreach ($detailRows as $item) {
                $outputData[] = [
                    'Receipt Date' => $item['payment_date'] ?? '',
                    'Receipt No' => $item['receipt_number'] ?? '',
                    'Receipt From' => $item['bill_type'] ?? '',
                    'Mode' => $item['payment_mode'] ?? '',
                    'Paid By' => $item['received_from'] ?? '',
                    'Payment Of' => $item['payment_note'] ?? '',
                    'Payment Reference' => $item['transaction_reference'] ?? '',
                    'Building / Unit' => ($item['society_unit_name'] ?? '').' / '.($item['unit_flat_number'] ?? ''),
                    'Paid Amount(TDS)' => $this->formatWriteOffAmount($item['payment_amount'] ?? 0),
                    'Write Off' => $this->formatWriteOffAmount($item['writeoff_amount'] ?? 0),
                    'Status' => ($item['transaction_status'] ?? ''),
                ];
            }
            // Add summary row from provided totals
            $summary = [
                'Receipt Date' => 'Total',
                'Receipt No' => '',
                'Receipt From' => '',
                'Mode' => '',
                'Paid By' => '',
                'Payment Of' => '',
                'Payment Reference' => '',
                'Building / Unit' => '',
                'Paid Amount(TDS)' => $this->formatWriteOffAmount($summaryTotals['paid_amount_total'] ?? 0),
                'Write Off' => $this->formatWriteOffAmount($summaryTotals['write_off_total'] ?? 0),
                'Status' => '',
            ];
               
            $this->data = [];

            // Calculate summary row
            $summary = [
                'Receipt Date' => 'Summary',
                'Receipt No' => '',
                'Receipt From' => '',
                'Mode' => '',
                'Paid By' => '',
                'Payment Of' => '',
                'Payment Reference' => '',
                'Building / Unit' => '',
                'Paid Amount(TDS)' => $this->formatWriteOffAmount($summaryTotals['paid_amount_total'] ?? 0),
                'Write Off' => $this->formatWriteOffAmount($summaryTotals['write_off_total'] ?? 0),
                'Status' => '',
            ];
            $outputData[] = $summary;
            if ($type == 'excel') {
                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'ReceiptReport_');
                $this->data['url'] = $data['data'];
            } else {
                $data = $this->hitCURLForGeneratePDF($MemberReceiptList, $this->headings, 'receiptReport');
                $this->data['url'] = $data['data'];
            }
        }
    }

    private function formatWriteOffAmount($value)
    {
        if ($value == 0 || $value === '0') {
            return '0';
        }
        return round($value, 2);
    }
}
