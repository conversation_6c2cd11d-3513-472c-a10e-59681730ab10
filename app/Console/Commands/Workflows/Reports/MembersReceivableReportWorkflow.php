<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class MembersReceivableReportWorkflow extends Workflow
{
    protected $signature = 'workflow:membersReceivableReport {input}';

    protected $description = 'Get Member Receivable Report';

    // protected $formatter = [
    //     'id' => '',
    //     'receipt_number' => '',
    //     'receipt_type' => '',
    //     'paid_by' => '',
    //     'invoice_number' => '',
    //     'payment_reference' => '',
    //     'unit' => '',
    //     'payment_amount' => '',
    //     'payment_date' => '',
    // ];

    protected $formatterByKeys = [
        'id'
    ];

    protected $schema = [
        'table' => [
            'tableTitle' => [
                'Members Receivable',
                'Summary'
            ],
            "select_by" => [
                "transaction_reference" => "All",
                "maintainance_due_more" => "Maintenance Dues greater than equal to",
                "maintainance_due_less" => "Maintenance Dues less than equal to",
                "incidental_due_more" => "Incident Dues greater than equal to",
                "incidental_due_less" => "Incident Dues less than equal to",
                "transaction_date" => "Due on Date"
            ],
            "filter_by" => [
                "type" => [
                    "title" => "Filter By",
                    'select_single' => true,
                    "options" => [
                        "maintenanace" => "Maintenanace",
                        "incident" => "Incident",
                    ],
                ],
            ],
            "actions" => [
                [
                    "title" => "Export Report",
                    "icon" => "ri-export-line",
                    "options" => [
                        [
                            "title" => "Print",
                            "icon" => "ri-file-2-line",
                            "api" => [
                                "type" => "download",
                                "url" => "/admin/income-details/membersReceivableReport/download/pdf",
                                "method" => "GET",
                            ]
                        ],
                        [
                            "title" => "PDF",
                            "icon" => "ri-file-pdf-2-line",
                            "api" => [
                                "type" => "download",
                                "url" => "/admin/income-details/membersReceivableReport/download/pdf",
                                "method" => "GET"
                            ]
                        ],
                        [
                            "title" => "Excel",
                            "icon" => "ri-file-excel-2-line",
                            "api" => [
                                "type" => "download",
                                "url" => "/admin/income-details/membersReceivableReport/download/excel",
                                "method" => "GET"
                            ]
                        ]
                    ]
                ]
            ],
            'fields' => [
                '*'
            ],
            'columns' => [
                [
                    [
                        'title' => 'Building/Unit',
                        'key' => 'building_unit_name'
                    ],
                    [
                        'title' => 'Primary Member',
                        'key' => 'member_name'
                    ],
                    [
                        'title' => 'Maintenance Dues',
                        'key' => 'maintainance_due'
                    ],
                    [
                        'title' => 'Incidental Dues',
                        'key' => 'indental_due'
                    ],
                    [
                        'title' => 'Credit Balance',
                        'key' => 'cr_bal'
                    ],
                    [
                        'title' => 'Ledger Balance',
                        'key' => 'ledger_bal'
                    ],
                ],
                [
                    [
                        'title' => 'Maintenance Dues',
                        'key' => 'maintenance_due_sum',
                    ],
                    [
                        'title' => 'Ledger Balance',
                        'key' => 'ledger_balance_sum',
                    ],
                    [
                        'title' => 'Incidental Dues',
                        'key' => 'incidental_due_sum',
                    ]
                ]

            ]
        ]
    ];

    public function apply()
    {
        $totals = [];
        $data = $this->action('datasource:membersReceivableReport', $this->pointer, $this->request);
        if ($data) {
            foreach ($data as &$r) {
                $r['maintenance_due'] = !empty($r['maintainance_due']) ? (float) round($r['maintainance_due'], 2) : 0; //(float) round($r['maintainance_due'], 2);
                $r['incidental_due'] = !empty($r['indental_due']) ? (float) round($r['indental_due'], 2) : 0;
                $r['ledger_balance'] = !empty($r['ledger_bal']) ? (float) round($r['ledger_bal'], 2) : 0;
                $r['cr_bal'] = !empty($r['cr_bal']) ? (float) round($r['cr_bal'], 2) : 0;
            }

                // totals
                $totals = [
                    'maintenance_due_sum' => round(array_sum(array_column($data, 'maintainance_due'))),
                    'incidental_due_sum'  => round(array_sum(array_column($data, 'indental_due'))),
                    'ledger_balance_sum'  => round(array_sum(array_column($data, 'ledger_bal'))),
                ];
        }
        
        $data = $data ? $data : [];
        $finall = [
            $data,
            [ $totals ]
        ];

        $staticOptions = [
            'maintenanace' => 'Maintenanace',  // or correct to 'maintenance' => 'Maintenance'
            'incident'     => 'Incident',
        ];
    
        $buildingList = $this
             ->action('datasource:buildinglist', $this->pointer, $this->request);

        $buildingOptions = collect($buildingList)
        ->pluck('soc_building_name', 'id')   // [ id => 'Foo', … ]
        ->map(function($name) {
            return 'Building '.$name;
        })
        ->toArray();                         


        // Then update only the specific part of the schema
        $this->meta['schema'] = $this->schema();
        $this->meta['schema']['table']['filter_by']['soc_building_name']['options'] =
        array_merge(
            $this->meta['schema']['table']['filter_by']['soc_building_name']['options'] ?? [],
            $buildingOptions
        );
    
        $this->meta['schema']['table']['filter_by']['soc_building_name']['select_single'] = true;
        // $this->meta['schema']['table']['filter_by']['soc_building_name']['title'] = 'Building';
        $this->data = $finall;
    }
    
    public function schema()
    {
        $filterType = $this->input['filters']['soc_building_name'] ?? null;

        $columns = [];
        $summary = [];

        if ($filterType === 'maintenanace') {
            $columns[] = [
                ['title' => 'Building/Unit', 'key' => 'building_unit_name'],
                ['title' => 'Primary Member', 'key' => 'member_name'],
                ['title' => 'Maintenance Dues', 'key' => 'maintainance_due'],
            ];
            $summary[] = [
                ['title' => 'Maintenance Dues', 'key' => 'maintenance_due_sum'],
            ];
        } elseif ($filterType === 'incident') {
            $columns[] = [
                ['title' => 'Building/Unit', 'key' => 'building_unit_name'],
                ['title' => 'Primary Member', 'key' => 'member_name'],
                ['title' => 'Incidental Dues', 'key' => 'indental_due'],
            ];
            $summary[] = [
                ['title' => 'Incidental Dues', 'key' => 'incidental_due_sum'],
            ];
        } else {
            // default full view
            $columns[] = [
                ['title' => 'Building/Unit', 'key' => 'building_unit_name'],
                ['title' => 'Primary Member', 'key' => 'member_name'],
                ['title' => 'Maintenance Dues', 'key' => 'maintainance_due'],
                ['title' => 'Incidental Dues', 'key' => 'indental_due'],
                ['title' => 'Credit Balance', 'key' => 'cr_bal'],
                ['title' => 'Ledger Balance', 'key' => 'ledger_bal'],
            ];
            $summary[] = [
                ['title' => 'Maintenance Dues', 'key' => 'maintenance_due_sum'],
                ['title' => 'Incidental Dues', 'key' => 'incidental_due_sum'],
                ['title' => 'Ledger Balance', 'key' => 'ledger_balance_sum'],
            ];
        }



        return [
            'table' => [
                'tableTitle' => ['Members Receivable', 'Summary'],
                'select_by' => [
                    'transaction_reference' => 'All',
                    'maintainance_due_more' => 'Maintenance Dues greater than equal to',
                    'maintainance_due_less' => 'Maintenance Dues less than equal to',
                    'incidental_due_more' => 'Incident Dues greater than equal to',
                    'incidental_due_less' => 'Incident Dues less than equal to',
                    'transaction_date' => 'Due on Date'
                ],
                'filter_by' => [
                    'soc_building_name' => [
                        'title' => 'Filter By',
                        'select_single' => true,
                        'options' => [
                            'maintenanace' => 'Maintenanace',
                            'incident' => 'Incident',
                        ],
                    ],
                    // Building filter will be injected later in apply()
                ],
                'actions' => [
                    [
                        'title' => 'Export Report',
                        'icon' => 'ri-export-line',
                        'options' => [
                            [
                                'title' => 'Print',
                                'icon' => 'ri-file-2-line',
                                'api' => [
                                    'type' => 'print',
                                    'url' => '/admin/income-details/membersReceivableReport/download/pdf',
                                    'method' => 'GET'
                                ]
                            ],
                            [
                                'title' => 'PDF',
                                'icon' => 'ri-file-pdf-2-line',
                                'api' => [
                                    'type' => 'download',
                                    'url' => '/admin/income-details/membersReceivableReport/download/pdf',
                                    'method' => 'GET'
                                ]
                            ],
                            [
                                'title' => 'Excel',
                                'icon' => 'ri-file-excel-2-line',
                                'api' => [
                                    'type' => 'download',
                                    'url' => '/admin/income-details/membersReceivableReport/download/excel',
                                    'method' => 'GET'
                                ]
                            ]
                        ]
                    ]
                ],
                'fields' => ['*'],
                'columns' => array_merge($columns, $summary),
            ]
        ];
    }

}
