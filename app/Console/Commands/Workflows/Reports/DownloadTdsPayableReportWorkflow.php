<?php

namespace App\Console\Commands\Workflows\Reports;

use App\Console\Commands\Workflow;

class DownloadTdsPayableReportWorkflow extends Workflow
{

    protected $signature = 'workflow:downloadTdsPayableReport {input}';

    protected $description = 'Download TDS Payable Report Workflow';

    protected $rules = [
        'from_date' => 'nullable|date_format:Y-m-d',
        'to_date' => 'nullable|date_format:Y-m-d'
    ];

    protected $rulesMessage = [
        'from_date.date_format' => 'From date should be in Y-m-t format',
        'to_date.date_format' => 'To date should be in Y-m-t format'
    ];

    protected $formatter = [
        'id' => '',
        'vendor_name' => '',
        'vendor_address' => '',
        'vendor_bill_num' => '',
        'vendor_pan_num' => '',
        'vendor_is_company' => '',
        'section' => '',
        'vendor_bill_date' => '',
        'vendor_bill_date_of_deduction' => '',
        'taxable_amount' => '',
        'gst_amount' => '',
        'total_bill_amount' => '',
        'rate' => '',
        'vendor_bill_tds' => '',
        'vendor_bill_amount' => ''
    ];

    protected $formatterByKeys = [
        'id'
    ];

    protected $headings = [
        'Vendor Name' => 'Vendor Name',
        'Address' => 'Address',
        'Bill Number' => 'Bill Number',
        'PAN Number' => 'PAN Number',
        'Company/Non Company/Individual' => 'Company/Non Company/Individual',
        'Section' => 'Section',
        'Date Of Amount Deducted' => 'Date Of Amount Deducted',
        'Rate Of Amount Deducted' => 'Rate Of Amount Deducted',
        'Taxable Amount' => 'Taxable Amount',
        'GST Amount' => 'GST Amount',
        'Total Amount' => 'Total Amount',
        'TDS Rate(%)' => 'TDS Rate(%)',
        'TDS Amount' => 'TDS Amount',
        'Payable Amount' => 'Payable Amount'
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $tds_payable = $this->action('datasource:tdspayableReport', $this->pointer, $this->request);

            // Extract detail records and totals from the nested array structure
            $detailRecords = $tds_payable[0] ?? [];
            $totalsRecord = $tds_payable[1][0] ?? [];
            // Format detail records to match screenshot headers
            $outputData = [];
            foreach ($detailRecords as $item) {
                $outputData[] = [
                    'Vendor Name' => $item['vendor_name'] ?? '',
                    'Address' => $item['vendor_address'] ?? '',
                    'Bill Number' => $item['vendor_bill_num'] ?? '',
                    'PAN Number' => $item['vendor_pan_num'] ?? '',
                    'Company/Non Company/Individual' => $this->getCompanyType($item['vendor_is_company'] ?? ''),
                    'Section' => $item['section'] ?? '',
                    'Date Of Amount Deducted' => $item['vendor_bill_date_of_deduction'] ?? '',
                    'Rate Of Amount Deducted' => $item['vendor_bill_date'] ?? '',
                    'Taxable Amount' => $this->formatWriteOffAmount($item['taxable_amount']) ?? '',
                    'GST Amount' => $this->formatWriteOffAmount($item['gst_amount']) ?? '',
                    'Total Amount' => $this->formatWriteOffAmount($item['total_bill_amount']) ?? '',
                    'TDS Rate(%)' => $this->formatWriteOffAmount($item['rate']) ?? '',
                    'TDS Amount' => $this->formatWriteOffAmount($item['vendor_bill_tds']) ?? '',
                    'Payable Amount' => $this->formatWriteOffAmount($item['vendor_bill_amount']) ?? ''
                ];
            }

            // Calculate totals for all numeric columns
            $totalTaxableAmount = array_sum(array_column($detailRecords, 'taxable_amount'));
            $totalGstAmount = array_sum(array_column($detailRecords, 'gst_amount'));
            $totalBillAmount = array_sum(array_column($detailRecords, 'total_bill_amount'));


            // Add Total row
            $outputData[] = [
                'Vendor Name' => 'Total',
                'Address' => '',
                'Bill Number' => '',
                'PAN Number' => '',
                'Company/Non Company/Individual' => '',
                'Section' => '',
                'Date Of Amount Deducted' => '',
                'Rate Of Amount Deducted' => '',
                'Taxable Amount' => $this->formatWriteOffAmount($totalTaxableAmount),
                'GST Amount' => $this->formatWriteOffAmount($totalGstAmount),
                'Total Amount' => $this->formatWriteOffAmount($totalBillAmount),
                'TDS Rate(%)' => $this->formatWriteOffAmount($totalsRecord['rate'] ?? ''),
                'TDS Amount' => $this->formatWriteOffAmount($totalsRecord['vendor_bill_tds'] ?? ''),
                'Payable Amount' => $this->formatWriteOffAmount($totalsRecord['vendor_bill_amount'] ?? '')
            ];
            $this->data = [];

            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'tds_payable_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($tds_payable, $this->headings, 'TDSPaybleReport');
                $this->data['url'] = $data['data'];
            }
        }
    }

    private function getCompanyType($type)
    {
        switch($type) {
            case 1:
                return 'Company';
            case 2:
                return 'Individual';
            default:
                return 'Non Company';
        }
    }

    private function formatWriteOffAmount($writeoff_amount)
    {
       if($writeoff_amount == 0)
       {
           return '0';
       }
       return $writeoff_amount;
    }

}