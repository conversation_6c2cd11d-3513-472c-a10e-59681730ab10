<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class DownloadGstReportWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadGstReport {input}';

    protected $description = 'Download Gst Report Workflow';

    protected $rules = [
    ];

    protected $rulesMessage = [
    ];

    protected $formatter = [
        'soc_building_name' => '',
        'unit_name' => '',
        'gst_number' => '',
        'bill_to' => '',
        'created_date' => '',
        'invoice_number' => '',
        'particular' => '',
        'amount' => ''
    ];

    protected $formatterByKeys = [
        'id'
    ];

    protected $headings = [
        'Building',
        'Unit',
        'GSTIN/UIN',
        'ITS No',
        'Party Name',
        'Invoice Number',
        'Invoice Date',
        'Particular',
        'Place Of Supply',
        'Reverse Charge',
        'Invoice Type',
        'Amount',
        'Total'
    ];

    public function apply()
    {

        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            $gstReportList = $this->action('datasource:gstReport', $this->pointer, $this->request);
                
            // Format data to match screenshot headers
            $outputData = [];
            foreach ($gstReportList as $item) {
                $outputData[] = [
                    'Building' => $item['soc_building_name'] ?? '',
                    'Unit' => $item['unit_name'] ?? '',
                    'GSTIN/UIN' => $item['gst_number'] ?? '-',
                    'ITS No' => '', // Not available in data
                    'Party Name' => $item['bill_to'] ?? '',
                    'Invoice Number' => $item['invoice_number'] ?? '',
                    'Invoice Date' => $item['created_date'] ?? '',
                    'Particular' => $item['particular'] ?? '',
                    'Place Of Supply' => $item['place_of_supply'] ?? '',
                    'Reverse Charge' => $item['reverse_charge'] ?? '',
                    'Invoice Type' => $item['invoice_type'] ?? '',
                    'Amount' => $this->formatWriteOffAmount($item['amount'] ?? 0),
                    'Total' => $this->formatWriteOffAmount($item['total'] ?? 0), // Same as amount for individual rows
                ];
            }
            
            $this->data = [];

            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'gstReportList_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($outputData, $this->headings, 'gstReportList_');
                $this->data['url'] = $data['data'];
            }
        }
    }

    private function formatWriteOffAmount($value)
    {
        if ($value == 0 || $value === '0') {
            return '0';
        }
        return round($value, 2);
    }
}