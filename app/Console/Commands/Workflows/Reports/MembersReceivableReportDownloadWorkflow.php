<?php

namespace App\Console\Commands\Workflows\Reports;

use App\Console\Commands\Workflow;

class MembersReceivableReportDownloadWorkflow extends Workflow
{
    protected $signature = 'workflow:membersReceivableReportDownload {input}';

    protected $description = 'Download Member Receivable Report';

    protected $rules = [
    ];

    protected $rulesMessage = [
    ];



    protected $headings = [
        'Building / Unit' => 'Building / Unit',
        'Primary Member' => 'Primary Member',
        'Maintenance Dues' => 'Maintenance Dues',
        'Incidental Dues' => 'Incidental Dues',
        'Credit Balance' => 'Credit Balance',
        'Ledger Balance' => 'Ledger Balance'
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            $memberReceiptData = $this->action('datasource:membersReceivableReport', $this->pointer, $this->request);
            //dd($memberReceiptData);
            // Process and format data to match screenshot headers
            $outputData = [];
            foreach ($memberReceiptData as $item) {
                $outputData[] = [
                    'Building / Unit' => $item['building_unit_name'] ?? '',
                    'Primary Member' => $item['member_name'] ?? '',
                    'Maintenance Dues' => $this->formatNumberForExcel($item['maintainance_due'] ?? 0),
                    'Incidental Dues' => $this->formatNumberForExcel($item['indental_due'] ?? 0),
                    'Credit Balance' => $this->formatNumberForExcel($item['cr_bal'] ?? 0),
                    'Ledger Balance' => $this->formatNumberForExcel($item['ledger_bal'] ?? 0)
                ];
            }
            
            // Calculate totals
            $totalMaintenanceDues = array_sum(array_column($memberReceiptData, 'maintainance_due'));
            $totalIncidentalDues = array_sum(array_column($memberReceiptData, 'indental_due'));
            $totalCreditBalance = array_sum(array_column($memberReceiptData, 'cr_bal'));
            $totalLedgerBalance = array_sum(array_column($memberReceiptData, 'ledger_bal'));

            // Add Total row
            $outputData[] = [
                'Building / Unit' => 'Total',
                'Primary Member' => '',
                'Maintenance Dues' => $this->formatNumberForExcel($totalMaintenanceDues),
                'Incidental Dues' => $this->formatNumberForExcel($totalIncidentalDues),
                'Credit Balance' => $this->formatNumberForExcel($totalCreditBalance),
                'Ledger Balance' => $this->formatNumberForExcel($totalLedgerBalance)
            ];

            $this->data = [];
            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'membersReceivableReport');
                $this->data['url'] = $data['data'];
            }
            else{
                $summaryData = $outputData[count($outputData) - 1];
                // For PDF, prepare data in the format expected by the PDF generator
                $finall = [
                    $outputData,
                    $summaryData  // Empty array for compatibility
                ];
                $data = $this->hitCURLForGeneratePDF($finall, $this->headings, 'membersReceivableReport');
                $this->data['url'] = $data['data'];
            }
        }
    }

    /**
     * Format numbers for Excel to ensure zero values are displayed as "0"
     * and proper decimal formatting
     */
    private function formatNumberForExcel($value)
    {
        if($value == 0)
       {
           return '0';
       }
       return round($value, 2);
    }
}