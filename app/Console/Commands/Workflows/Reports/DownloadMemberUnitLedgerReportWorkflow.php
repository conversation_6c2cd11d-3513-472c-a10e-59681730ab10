<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class DownloadMemberUnitLedgerReportWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadMemberUnitLedgerReport {input}';

    protected $description = 'Download Member Unit Ledger Report Workflow';

    /*protected $rules = [
        'from_date' => 'nullable|date_format:Y-m-d',
        'to_date' => 'nullable|date_format:Y-m-d',
        'unit' => 'required',
    ];

    protected $rulesMessage = [
        'from_date.date_format' => 'From date should be in Y-m-t format',
        'to_date.date_format' => 'To date should be in Y-m-t format',
        'unit.required' => 'Unit is required',
    ];*/

    protected $formatter = [
        'id' => '',
        'transaction_date' => '',
        'particulars' => '',
        'voucher_type' => '',
        'voucher_reference_number' => '',
        'debit' => '',
        'credit' => '',
    ];

    protected $formatterByKeys = [
        'id'
    ];

    protected $headings = [
        'id',
        'transaction_date',
        'particulars',
        'voucher_type',
        'voucher_reference_number',
        'debit',
        'credit',
    ];

    public function apply()
    {

        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $member_unit_ledger = $this->action('datasource:MembersUnitLedgerReport', $this->pointer, $this->request);
            
            $this->data = [];

            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($member_unit_ledger, $this->headings, 'member_unit_ledger_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($member_unit_ledger, $this->headings, 'member_unit_ledger');
                
                $this->data['url'] = $data['data'];
            }
        }
    }
}