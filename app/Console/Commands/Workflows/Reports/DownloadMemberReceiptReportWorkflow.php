<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class DownloadMemberReceiptReportWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadMemberReceiptReport {input}';

    protected $description = 'Download Member Receipt Report Workflow';

    protected $rules = [
        'from_date' => 'nullable|date_format:Y-m-d',
        'to_date' => 'nullable|date_format:Y-m-d',
    ];

    protected $rulesMessage = [
        'from_date.date_format' => 'From date should be in Y-m-t format',
        'to_date.date_format' => 'To date should be in Y-m-t format'
    ];

    protected $formatter = [
        'id' => '',
        'receipt_number' => '',
        'receipt_type' => '',
        'paid_by' => '',
        'invoice_number' => '',
        'payment_reference' => '',
        'unit' => '',
        'payment_amount' => '',
        'payment_date' => '',
    ];

    protected $formatterByKeys = [
        'id'
    ];

    protected $headings = [
        'Receipt Date',
        'Receipt No',
        'Receipt Type',
        'Paid By',
        'Invoice No',
        'Payment Reference',
        'Building / Unit',
        'Paid Amount',
    ];

    public function apply()
    {

        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            $memberReceiptData = $this->action('datasource:MemberReceiptReport', $this->pointer, $this->request );
            $dataActual = $memberReceiptData[0] ?? [];
            
            $summaryTotals = $memberReceiptData[1] ?? [];
            // Format data to match screenshot headers
            $outputData = [];
            foreach ($dataActual as $item) {
                $outputData[] = [
                    'Receipt Date' => $item['receipt_date'] ?? '',
                    'Receipt No' => $item['receipt_number'] ?? '',
                    'Receipt Type' => $item['receipt_type'] ?? '',
                    'Paid By' => $item['paid_by'] ?? '',
                    'Invoice No' => $item['invoice_number'] ?? '',
                    'Payment Reference' => $item['payment_reference'] ?? '',
                    'Building / Unit' => $item['unit'] ?? '',
                    'Paid Amount' => $item['payment_amount'] ?? '',
                ];
            }

            // Add Total row
            $totalRow = [
                'Receipt Date' => 'Total',
                'Receipt No' => '',
                'Receipt Type' => '',
                'Paid By' => '',
                'Invoice No' => '',
                'Payment Reference' => '',
                'Building / Unit' => '',
                'Paid Amount' => $summaryTotals[0]['total_amount'] ?? '',
            ];
            $outputData[] = $totalRow;

            $this->data = [];

            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'memberReceiptData_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($memberReceiptData, $this->headings, 'IncomeReceiptReport');
                $this->data['url'] = $data['data'];
            }
        }
    }
}