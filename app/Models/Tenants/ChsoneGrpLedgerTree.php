<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Tenants;

use App\Models\TenantModel;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\Tenants\{
    SocAccountFinancialYearMaster,
    ChsoneLedgerTransaction
};

/**
 * Class ChsoneGrpLedgerTree
 *
 * @property int $ledger_account_id
 * @property int|null $soc_id
 * @property string $ledger_account_name
 * @property string|null $nature_of_account
 * @property int|null $parent_id
 * @property string|null $report_head
 * @property string|null $operating_type
 * @property int|null $context_ref_id
 * @property string|null $context
 * @property Carbon|null $ledger_start_date
 * @property Carbon $added_on
 * @property int|null $status
 * @property int $created_by
 * @property string $entity_type
 * @property string|null $behaviour
 * @property string|null $defined_by
 *
 * @package App\Models\Tenants
 */
class ChsoneGrpLedgerTree extends TenantModel
{
    protected $table = 'chsone_grp_ledger_tree';
    protected $primaryKey = 'ledger_account_id';
    public $timestamps = false;

    protected $casts = [
        'soc_id' => 'int',
        'parent_id' => 'int',
        'context_ref_id' => 'int',
        'ledger_start_date' => 'datetime',
        'added_on' => 'datetime',
        'status' => 'int',
        'created_by' => 'int'
    ];

    protected $fillable = [
        'soc_id',
        'ledger_account_name',
        'nature_of_account',
        'parent_id',
        'report_head',
        'operating_type',
        'context_ref_id',
        'context',
        'ledger_start_date',
        'added_on',
        'status',
        'created_by',
        'entity_type',
        'behaviour',
        'defined_by'
    ];

    public function getLedger($ledger_acct_id, $return_object = true)
    {
        $ledger = DB::connection('tenant')->table($this->table)->where('ledger_account_id', $ledger_acct_id)->first();

        if ($ledger) {
            if ($return_object) {
                return $ledger;
            } else {
                return json_decode(json_encode($ledger), true);
            }
        } else {
            return false;
        }
    }

    /**
     * The Component action `_getLedgerProps` is called to get ledger property like behaviour, nature of account, report head etc.
     * @method _getLedgerProps.
     * @access public
     * @param integer $parent_group parent group id
     * @return array
     * @uses \App\Models\GrpLedgTree::__construct()
     */
    public function _getLedgerProps($parent_group)
    {
        $array = [];

        if (is_numeric($parent_group)) {
            $ledger_props = DB::connection('tenant')->table($this->table)->where('ledger_account_id', $parent_group)
                ->select('ledger_account_name', 'behaviour', 'nature_of_account', 'report_head', 'context')
                ->first();
        }

        if (!empty($ledger_props)) {
            $array["ledger_account_name"] = $ledger_props->ledger_account_name;
            $array["behaviour"] = $ledger_props->behaviour;
            $array["nature_account"] = $ledger_props->nature_of_account;
            $array["report_head"] = $ledger_props->report_head;
            $array["context"] = $ledger_props->context;
        }

        return $array;
    }

    public function fetchLedgerTree($whereConditions)
    {
        $currentTab = $this->input['current_tab'] ?? 'Asset';

        $parentIdMap = [
            'asset' => 1,
            'liability' => 2,
            'income' => 3,
            'expense' => 4,
            'default' => 0
        ];

        $parentId = $parentIdMap[strtolower($currentTab)] ?? $parentIdMap['default'];

        $whereClause = isset($_POST['entity_type']) && $_POST['entity_type'] == 'group' ? "entity_type = 'group'" : "parent_id = $parentId";
        //$whereClause = '';
        foreach ($whereConditions as $column => $value) {
            $whereClause .= $whereClause ? ' AND ' : ' WHERE ';
            $whereClause .= "$column = '$value'";
        }
        $query = "WITH RECURSIVE LedgerTree AS (
            SELECT ledger_account_id, ledger_account_name, nature_of_account, entity_type, behaviour, parent_id, 0 AS level, status, context
            FROM chsone_grp_ledger_tree
            WHERE $whereClause

            UNION ALL

            SELECT child.ledger_account_id, child.ledger_account_name, child.nature_of_account, child.entity_type, child.behaviour, child.parent_id, parent.level + 1, child.status, child.context
            FROM chsone_grp_ledger_tree AS child
            INNER JOIN LedgerTree AS parent ON child.parent_id = parent.ledger_account_id
        )
        SELECT *
        FROM LedgerTree
        ORDER BY level, ledger_account_name;";

        //$query = $this->filter($query);

        DB::connection('tenant')->statement($query);

        // Fetch the results if needed
        $result = DB::connection('tenant')->table('chsone_grp_ledger_tree')->get();

        $tree = [];
        $tree = $this->buildTree1($result, $parentId, $whereConditions, $initialCall = true);

        return $tree[0];
    }

    public function buildTree($data, $parentId, $whereConditions, $initialCall = true)
    {
        $tree = [];

        foreach ($data as $row) {
            $matched = true;
            if (!$initialCall) {
                foreach ($whereConditions as $column => $value) {
                    if ($row->$column != $value || $value == '') {
                        $matched = false;
                        break;
                    }
                }
            }
            //dd($data);
            if ($row->parent_id == $parentId && $matched) {
                $node = [
                    'id' => $row->ledger_account_id,
                    'ledger_account_name' => $row->ledger_account_name,
                    'nature_of_account' => $row->nature_of_account,
                    'entity_type' => $row->entity_type,
                    'behaviour' => $row->behaviour,
                    'context' => $row->context,
                    'status' => $row->status,
                    'rows' => $this->buildTree($data, $row->ledger_account_id, $whereConditions, false)
                ];

                $tree[] = $node;
            }
        }

        return $tree;
    }

    public function buildTree1($data, $parentId, $whereConditions, $initialCall = true)
    {
        $tree = [];
    
        foreach ($data as $row) {
            $matched = true;
    
            if (!$initialCall) {
                foreach ($whereConditions as $column => $value) {
                    if ($row->$column != $value || $value == '') {
                        $matched = false;
                        break;
                    }
                }
            }
    
            if ($row->parent_id == $parentId && $matched) {
                $children = $this->buildTree1($data, $row->ledger_account_id, $whereConditions, false);
    
                $node = [
                    'id' => $row->ledger_account_id,
                    'ledger_account_name' => $row->ledger_account_name,
                    'nature_of_account' => $row->nature_of_account,
                    'entity_type' => $row->entity_type,
                    'behaviour' => $row->behaviour,
                    'context' => $row->context,
                    'status' => $row->status,
                ];
    
                // Strictly only include 'rows' if non-empty
                if (is_array($children) && count($children) > 0) {
                    $node['rows'] = $children;
                }
    
                $tree[] = $node;
            }
        }
    
        return $tree;
    }
    


    public function getLedgerDetail($soc_id, $data = array())
    {
        //Check whether ledger exist or not
        $condition = 'soc_id=' . $soc_id;
        if (!empty($data['ledger_id'])) {
            $condition .= ' AND ledger_account_id="' . $data['ledger_id'] . '"';
        }
        if (!empty($data['ledger_name'])) {
            $condition .= ' AND ledger_account_name="' . $data['ledger_name'] . '"';
        }
        if (!empty($data['behaviour'])) {
            $condition .= ' AND behaviour="' . $data['behaviour'] . '"';
        }
        if (!empty($data['context'])) {
            $condition .= ' AND context="' . $data['context'] . '"';
        }
        if (!empty($data['entity_type'])) {
            $condition .= ' AND entity_type="' . $data['entity_type'] . '"';
        }
        if (!empty($data['context_ref_id'])) {
            $condition .= ' AND context_ref_id="' . $data['context_ref_id'] . '"';
        }
        $objLedgerDetail = ChsoneGrpLedgerTree::whereRaw($condition)->first();

        if (!empty($objLedgerDetail)) {
            return $objLedgerDetail->toArray();
        }
        return false;
    }

    public function createNewLedgerExit($soc_id, $data = [])
    {
        $ledger_name = $data['ledger_name'];
        $parent_id = $data['parent_id'];
        $behaviour = $data['behaviour'];
        $context = $data['context'];

        // Check whether ledger exists or not
        $objBookerLedger = ChsoneGrpLedgerTree::where('soc_id', $soc_id)
            ->where('entity_type', ENTITY_TYPE_LEDGER)
            ->where('ledger_account_name', $ledger_name)
            ->where('parent_id', $parent_id)
            ->where('behaviour', $behaviour)
            ->first();

        if (!empty($objBookerLedger)) {
            return $objBookerLedger->ledger_account_id;
        } else {
            // Create new ledger if it does not exist
            $ledger_account_id = $this->manipulate($soc_id, $ledger_name, ENTITY_TYPE_LEDGER, "", $parent_id, $behaviour, '', 0, '', '', null, $context);

            if (is_string($ledger_account_id) && strpos($ledger_account_id, 'DUP') === false) {
                return $ledger_account_id;
            }
        }

        return false;
    }

    public function manipulateByArray($arrData = array())
    {
        $soc_id = $arrData['soc_id'] ?? '';
        $name = $arrData['ledger_account_name'] ?? '';
        $entity_type = $arrData['entity_type'] ?? '';
        $grp_ledg_id = $arrData['grp_ledg_id'] ?? '';
        $parent_group = $arrData['parent_group'] ?? '';
        $behaviour = $arrData['behaviour'] ?? '';
        $ledg_id = $arrData['ledger_account_id'] ?? '';
        $opening_balance = $arrData['opening_balance'] ?? 0;
        $update_led_id = $arrData['update_led_id'] ?? '';
        $ledger_start_date = $arrData['ledger_start_date'] ?? '';
        $ledger_type = $arrData['ledger_type'] ?? '';
        $context = $arrData['context'] ?? '';
        $nature = $arrData['nature'] ?? '';
        $input = $arrData['input'] ?? '';
        $dup = $this->_checkGroupNameDuplication($soc_id, $parent_group, $name, $ledg_id, $update_led_id);

        $arrCurrentFYDetailObj = new SocAccountFinancialYearMaster();
        $arrCurrentFYDetail = $arrCurrentFYDetailObj->getCurrentFYDetail($soc_id);
        $arrCurrentFYDetail = json_decode(json_encode($arrCurrentFYDetail), true);
        $ledger_start_date = $arrCurrentFYDetail['fy_start_date'];

        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));


            $grp_ledg_tree = new ChsoneGrpLedgerTree();
            //for edit ledger and edit group case,check if ledger id is passed, if yes then get records
            if ($ledg_id) {
                $grp_ledg_tree = $this->getLedger($ledg_id);
            }
            $grp_ledg_tree->entity_type = $entity_type;
            $grp_ledg_tree->soc_id = $soc_id;
            $grp_ledg_tree->ledger_account_name = $name;
            $grp_ledg_tree->ledger_start_date = $ledger_start_date;
            $grp_ledg_tree->context_ref_id = 0;

            if (!empty($ledger_type) && $ledger_type != "NULL" && $ledger_type != '') {
                $grp_ledg_tree->operating_type = $ledger_type;
            } else {
                $grp_ledg_tree->operating_type = '';
            }



            if (!empty($grp_ledg_id)) {
                $grp_ledg_tree->ledger_account_id = $grp_ledg_id;
            }

            if ($update_led_id != '') {
                //echo $update_led_id;exit;
                $grp_ledg_tree->ledger_account_id = $update_led_id;
            }

            $ledger_props = [];
            if (!empty($parent_group)) {
                $grp_ledg_tree->parent_id = $parent_group;
                $ledger_props = $this->_getLedgerProps($parent_group);
            } else {
                $grp_ledg_tree->parent_id = HEAD_GROUP_VAL;
            }

            if (!empty($behaviour)) {
                $grp_ledg_tree->behaviour = $behaviour;
                // Set nature_of_account based on behaviour, adjust mapping as needed
                $natureAccountMap = [
                    'income' => 'cr',
                    'expense' => 'dr',
                    'asset' => 'dr',
                    'liability' => 'cr'
                ];
                $reportHeadMap = [
                    'income'    => "Profit & Loss",
                    'expense'   => "Profit & Loss",
                    'asset'     => "Balance Sheet",
                    'liability' => "Balance Sheet"
                ];
                $grp_ledg_tree->nature_of_account = $natureAccountMap[$behaviour] ?? '';
                // Replace with your actual config retrieval logic if needed
                $grp_ledg_tree->report_head = $reportHeadMap[$behaviour] ?? '';
            } else {
                $grp_ledg_tree->behaviour = $ledger_props["behaviour"] ?? '';
                $grp_ledg_tree->nature_of_account = $ledger_props["nature_account"] ?? '';
                $grp_ledg_tree->report_head = $ledger_props["report_head"] ?? '';
            }
            if (!empty($context) && $context != '') {
                $grp_ledg_tree->context = $context;
            } else {
                $grp_ledg_tree->context = $ledger_props["context"];
            }
            $grp_ledg_tree->defined_by = USER;
            $grp_ledg_tree->status = ACTIVE;
            $grp_ledg_tree->added_on = date("Y-m-d H:i:s");
            $grp_ledg_tree->created_by = $input['user_id'] ?? 0;

            if ($grp_ledg_tree->save()) {
                //Opening balance should not go in case on Income Expense ledger
                if (!in_array(strtolower($grp_ledg_tree->behaviour), array(INCOME, EXPENSE))) {
                    $txn = new ChsoneLedgerTransaction();
                    $txn_date = $ledger_start_date; //date("Y-m-d H:i:s");
                    $narration = 'entry for opening balance';
                    $from_txn_id = (!empty($nature) && strtolower($nature) == 'cr') ? $nature : '';

                    if ($txn_id = $txn->addTxn($grp_ledg_tree->ledger_account_id, $opening_balance, $narration, $txn_date, "", "", $from_txn_id, "", "", "", $name, $is_opning = 1, /*$is_reco*/ 0, $soc_id)) {
                        #$this->soc_db_w->commit();
                    } else {
                        #$this->soc_db_w->rollback();
                    }
                    /*if ($txn_id = $txn->addTxn($grp_ledg_tree->ledger_account_id, $opening_balance, $narration, $txn_date, "", $grp_ledg_tree->nature_of_account, "", "", "", "", $name, $is_opning = 1, 0)) {
                        #$this->soc_db_w->commit();
                    } else {
                        #$this->soc_db_w->rollback();
                    }*/
                }
                return $grp_ledg_tree->ledger_account_id;
            } else {
                return false;
            }
        } else {
            return "DUP" . $dup;
        }
    }

    public function _checkGroupNameDuplication($soc_id, $parent_group, $name, $ledg_id, $update_led_id)
    {
        $name = strtolower(trim($name));

        $query = ChsoneGrpLedgerTree::where('soc_id', $soc_id)
            ->whereRaw('LOWER(ledger_account_name) = ?', [$name]);

        if ($ledg_id) {
            $query->where('ledger_account_id', '!=', $ledg_id);
        }

        if ($update_led_id) {
            $query->where('ledger_account_id', '!=', $update_led_id);
        }

        $ledgers_count = $query->count();
        if ($ledgers_count) {
            return true;
        }
        return false;
    }

    public function getLedgerTreeByIdsOptimize($ids = '', $params = [])
    {
        $condition = implode(',', $ids);

        $query = ChsoneGrpLedgerTree::select(
            'chsone_grp_ledger_tree.ledger_account_name',
            'chsone_grp_ledger_tree.nature_of_account',
            'chsone_grp_ledger_tree.behaviour',
            'chsone_grp_ledger_tree.entity_type',
            'chsone_grp_ledger_tree.ledger_account_id',
            'chsone_grp_ledger_tree.parent_id AS parent',
            'chsone_grp_ledger_tree.status',
            'chsone_grp_ledger_tree.context',
            'chsone_grp_ledger_tree.operating_type',
            DB::raw('SUM(CASE WHEN tr.transaction_type = "dr" THEN tr.transaction_amount ELSE 0 END) AS drbal'),
            DB::raw('SUM(CASE WHEN tr.transaction_type = "dr" THEN tr.transaction_amount ELSE 0 END) AS drbalpre'),
            DB::raw('SUM(CASE WHEN tr.transaction_type = "cr" THEN tr.transaction_amount ELSE 0 END) AS crbal'),
            DB::raw('SUM(CASE WHEN tr.transaction_type = "cr" THEN tr.transaction_amount ELSE 0 END) AS crbalpre'),
            DB::raw('SUM(CASE WHEN tr.is_opening_balance != 0 THEN tr.transaction_amount ELSE 0 END) AS openingbal'),
            'openingbal_type.transaction_type AS openingbaltype',
            DB::raw('SUM(CASE WHEN tr.is_opening_balance != 0 THEN tr.transaction_amount ELSE 0 END) AS openingbalpre'),
            'openingbal_type_pre.transaction_type AS openingbaltypepre'
        )
            ->leftJoin(DB::raw("(SELECT ledger_account_id, SUM(transaction_amount) AS transaction_amount FROM chsone_ledger_transactions WHERE soc_id = :soc_id AND transaction_date BETWEEN :start_date AND :end_date AND is_opening_balance = 0 GROUP BY ledger_account_id) AS tr"), 'tr.ledger_account_id', '=', 'chsone_grp_ledger_tree.ledger_account_id')
            ->leftJoin(DB::raw("(SELECT ledger_account_id, transaction_type FROM chsone_ledger_transactions WHERE soc_id = :soc_id AND transaction_date BETWEEN :start_date AND :end_date AND is_opening_balance != 0 GROUP BY ledger_account_id) AS openingbal_type"), 'openingbal_type.ledger_account_id', '=', 'chsone_grp_ledger_tree.ledger_account_id')
            ->leftJoin(DB::raw("(SELECT ledger_account_id, SUM(transaction_amount) AS transaction_amount FROM chsone_ledger_transactions WHERE soc_id = :soc_id AND transaction_date BETWEEN :start_date_pre AND :end_date_pre AND is_opening_balance = 0 GROUP BY ledger_account_id) AS tr_pre"), 'tr_pre.ledger_account_id', '=', 'chsone_grp_ledger_tree.ledger_account_id')
            ->leftJoin(DB::raw("(SELECT ledger_account_id, transaction_type FROM chsone_ledger_transactions WHERE soc_id = :soc_id AND transaction_date BETWEEN :start_date_pre AND :end_date_pre AND is_opening_balance != 0 GROUP BY ledger_account_id) AS openingbal_type_pre"), 'openingbal_type_pre.ledger_account_id', '=', 'chsone_grp_ledger_tree.ledger_account_id')
            ->whereRaw("chsone_grp_ledger_tree.ledger_account_id IN ($condition)")
            ->orWhereRaw("FIND_IN_SET(chsone_grp_ledger_tree.parent_id, :ids)")
            ->groupBy('chsone_grp_ledger_tree.ledger_account_id')
            ->orderBy('chsone_grp_ledger_tree.parent_id')
            ->orderBy('chsone_grp_ledger_tree.ledger_account_name')
            ->get([
                'chsone_grp_ledger_tree.*',
                'openingbal_type.transaction_type AS openingbaltype',
                'openingbal_type_pre.transaction_type AS openingbaltypepre',
            ], [
                'soc_id' => $params['soc_id'],
                'start_date' => $params['start_date'],
                'end_date' => $params['end_date'],
                'start_date_pre' => $params['start_date_pre'],
                'end_date_pre' => $params['end_date_pre'],
                'ids' => $ids,
            ]);

        return $query;
    }

    public function getLedgerTreeByConditionOptimize($fin_cnd, $params = [])
    {
        $start_date = $params['start_date'];
        $end_date = $params['end_date'];
        $start_date_pre = $params['start_date_pre'];
        $end_date_pre = $params['end_date_pre'];
        $soc_id = $params['soc_id'];
        $caseWhenCondition = $fin_cnd ?
            'WHEN 1 THEN @idlist := CONCAT(chsone_grp_ledger_tree.ledger_account_id)' :
            'WHEN FIND_IN_SET(parent_id, @idlist) THEN @idlist := CONCAT(@idlist, ",", chsone_grp_ledger_tree.ledger_account_id)';
        $final_query = ChsoneGrpLedgerTree::select(
            'chsone_grp_ledger_tree.ledger_account_name',
            'chsone_grp_ledger_tree.nature_of_account',
            'chsone_grp_ledger_tree.behaviour',
            'chsone_grp_ledger_tree.entity_type',
            'chsone_grp_ledger_tree.ledger_account_id',
            'chsone_grp_ledger_tree.parent_id AS parent',
            'chsone_grp_ledger_tree.status',
            'chsone_grp_ledger_tree.context',
            'chsone_grp_ledger_tree.operating_type',
            \DB::raw('R1.amount AS drbal'),
            \DB::raw('R2.amount AS drbalpre'),
            \DB::raw('R3.amount AS crbal'),
            \DB::raw('R4.amount AS crbalpre'),
            \DB::raw('R5.amount AS openingbal'),
            \DB::raw('R6.transaction_type AS openingbaltype'),
            \DB::raw('R7.amount AS openingbalpre'),
            \DB::raw('R8.transaction_type AS openingbaltypepre'),
            \DB::raw("CASE $caseWhenCondition ELSE @idlist END AS checkId")
        )->orderBy("parent_id")
            ->orderBy("ledger_account_name")
            ->leftJoinSub(function ($query) use ($soc_id, $start_date, $end_date) {
                $query->selectRaw('SUM(transaction_amount) as amount, chsone_ledger_transactions.ledger_account_id')
                    ->from('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->whereBetween('transaction_date', [$start_date, $end_date])
                    ->where('transaction_type', 'dr')
                    ->where('is_opening_balance', 0)
                    ->groupBy('chsone_ledger_transactions.ledger_account_id');
            }, 'R1', 'chsone_grp_ledger_tree.ledger_account_id', '=', 'R1.ledger_account_id')
            ->leftJoinSub(function ($query) use ($soc_id, $start_date_pre, $end_date_pre) {
                $query->selectRaw('SUM(transaction_amount) as amount, chsone_ledger_transactions.ledger_account_id')
                    ->from('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->whereBetween('transaction_date', [$start_date_pre, $end_date_pre])
                    ->where('transaction_type', 'dr')
                    ->where('is_opening_balance', 0)
                    ->groupBy('chsone_ledger_transactions.ledger_account_id');
            }, 'R2', 'chsone_grp_ledger_tree.ledger_account_id', '=', 'R2.ledger_account_id')
            ->leftJoinSub(function ($query) use ($soc_id, $start_date, $end_date) {
                $query->selectRaw('SUM(transaction_amount) as amount, chsone_ledger_transactions.ledger_account_id')
                    ->from('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->whereBetween('transaction_date', [$start_date, $end_date])
                    ->where('transaction_type', 'cr')
                    ->where('is_opening_balance', 0)
                    ->groupBy('chsone_ledger_transactions.ledger_account_id');
            }, 'R3', 'chsone_grp_ledger_tree.ledger_account_id', '=', 'R3.ledger_account_id')
            ->leftJoinSub(function ($query) use ($soc_id, $start_date, $end_date) {
                $query->selectRaw('SUM(transaction_amount) as amount, chsone_ledger_transactions.ledger_account_id')
                    ->from('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->whereBetween('transaction_date', [$start_date, $end_date])
                    ->where('transaction_type', 'cr')
                    ->where('is_opening_balance', 0)
                    ->groupBy('chsone_ledger_transactions.ledger_account_id');
            }, 'R4', 'chsone_grp_ledger_tree.ledger_account_id', '=', 'R4.ledger_account_id')
            ->leftJoinSub(function ($query) use ($soc_id, $start_date, $end_date) {
                $query->selectRaw('SUM(transaction_amount) as amount, chsone_ledger_transactions.ledger_account_id')
                    ->from('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->whereBetween('transaction_date', [$start_date, $end_date])
                    ->where('is_opening_balance', '!=', 0)
                    ->groupBy('chsone_ledger_transactions.ledger_account_id');
            }, 'R5', 'chsone_grp_ledger_tree.ledger_account_id', '=', 'R5.ledger_account_id')
            ->leftJoinSub(function ($query) use ($soc_id, $start_date, $end_date) {
                $query->selectRaw('transaction_type, chsone_ledger_transactions.ledger_account_id')
                    ->from('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->whereBetween('transaction_date', [$start_date, $end_date])
                    ->where('is_opening_balance', '!=', 0)
                    ->groupBy('chsone_ledger_transactions.ledger_account_id');
            }, 'R6', 'chsone_grp_ledger_tree.ledger_account_id', '=', 'R6.ledger_account_id')
            ->leftJoinSub(function ($query) use ($soc_id, $start_date_pre, $end_date_pre) {
                $query->selectRaw('transaction_amount as amount, chsone_ledger_transactions.ledger_account_id')
                    ->from('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->whereBetween('transaction_date', [$start_date_pre, $end_date_pre])
                    ->where('is_opening_balance', '!=', 0)
                    ->groupBy('chsone_ledger_transactions.ledger_account_id');
            }, 'R7', 'chsone_grp_ledger_tree.ledger_account_id', '=', 'R7.ledger_account_id')
            ->leftJoinSub(function ($query) use ($soc_id, $start_date_pre, $end_date_pre) {
                $query->selectRaw('transaction_type, chsone_ledger_transactions.ledger_account_id')
                    ->from('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->whereBetween('transaction_date', [$start_date_pre, $end_date_pre])
                    ->where('is_opening_balance', '!=', 0)
                    ->groupBy('chsone_ledger_transactions.ledger_account_id');
            }, 'R8', 'chsone_grp_ledger_tree.ledger_account_id', '=', 'R8.ledger_account_id')
            ->whereRaw('@idlist IS NOT NULL')
            ->get();

        // Handle the result as needed
        return $final_query;
    }

    public function getLedgerDetails($soc_id, $ledger_account_id)
    {
        return $this->where('soc_id', $soc_id)
            ->where('ledger_account_id', $ledger_account_id)
            ->first();
    }

    public function getParentGroupId($arrData)
    {
        $parent = ChsoneGrpLedgerTree::where('soc_id', $arrData['soc_id'])
            ->where('ledger_account_name', $arrData['group_name'])
            ->where('entity_type', ENTITY_TYPE_GROUP)
            ->where('behaviour', $arrData['behaviour'])
            ->where('context', $arrData['context'])
            ->first();
        if ($parent) {
            return $parent;
        } else {
            return false;
        }
    }

    public function disableLedger($ledger_id, $soc_id)
    {
        // Fetch the ledger with the given `ledger_account_id` and `soc_id`
        $ledger = ChsoneGrpLedgerTree::where('ledger_account_id', $ledger_id)
            ->where('soc_id', $soc_id)
            ->first();

        if (!$ledger) {
            $this->status = 'error';
            $this->message = 'Ledger not found.';
            $this->statusCode = 400;
            return 0;
        }

        $data = $ledger->toArray();

        // Traverse and update parent records if necessary
        while (!empty($data['parent_id']) && $data['parent_id'] != 0 && $data['status'] == 0) {
            if (!empty($data['parent_id']) && $data['parent_id'] != 1 && $data['status'] == 0) {
                $ledgerParent = ChsoneGrpLedgerTree::where('ledger_account_id', $data['parent_id'])
                    ->where('soc_id', $soc_id)
                    ->first();

                if ($ledgerParent) {
                    $data['parent_id'] = $ledgerParent->parent_id;
                    $ledgerParent->status = 1;
                    $ledgerParent->save();
                } else {
                    $data['parent_id'] = 0;
                }
            } else {
                $data['parent_id'] = 0;
            }
        }

        // Toggle the status of the current ledger
        if ($ledger->status == 0) {
            $ledger->status = 1;
        } else {
            $ledger->status = 0;
        }
        $ledger->save();

        return 1;
        //return response()->json(['message' => 'Ledger status updated successfully.']);
    }
}
