<?php

namespace App\Http\Traits;

use Illuminate\Support\Facades\Config;
use App\Models\Tenants\{
    ChsoneGrpLedgerTree,
    ChsoneLedgerTransaction,
    ChsoneCreditAccount,
    ChsoneVoucherMaster
};
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;


trait CommonFunctionTraits
{
    private function getCounterEntryLedgerName($transactionList = [])
    {
        $ledgerNameArr = [];

        foreach ($transactionList as $txnList) {
            $ledgerAccName = '';

            if ($txnList->txn_from_id == null || $txnList->txn_from_id == '' || $txnList->txn_from_id == 0) {
                $txnListNew = $this->tenantDB()
                    ->table("chsone_ledger_transactions")
                    ->where("txn_from_id", $txnList->txn_id)
                    ->get()
                    ->toArray();

                if (!empty($txnListNew)) {
                    $counterEntry = $txnListNew;
                    $ledgerAccName = $counterEntry[0]->ledger_account_name;

                    if (empty($ledgerAccName)) {
                        $counterEntryLedgerDetails = $this->tenantDB()
                            ->table("chsone_grp_ledger_tree")
                            ->where('ledger_account_id', $counterEntry[0]->ledger_account_id)
                            ->first();
                        if ($counterEntryLedgerDetails) {
                            $ledgerAccName = $counterEntryLedgerDetails->ledger_account_name;
                        }
                        unset($counterEntryLedgerDetails);
                    }

                    $ledgerNameArr[] = [
                        "name" => "To " . $ledgerAccName,
                        "ledger_id" => $counterEntry[0]->ledger_account_id,
                    ];
                } else {
                    $ledgerNameArr[] = [
                        "name" => "For " . $txnList->ledger_account_name,
                        "ledger_id" => $txnList->ledger_account_id,
                    ];
                }
            } else {
                $txnListNew = $this->tenantDB()
                    ->table("chsone_ledger_transactions")
                    ->where("txn_id", $txnList->txn_from_id)
                    ->get()
                    ->toArray();

                if (!empty($txnListNew)) {
                    $counterEntry = $txnListNew;
                    $ledgerAccName = $counterEntry[0]->ledger_account_name;

                    if (empty($ledgerAccName)) {
                        $counterEntryLedgerDetails = $this->tenantDB()
                            ->table("chsone_grp_ledger_tree")
                            ->where('ledger_account_id', $counterEntry[0]->ledger_account_id)
                            ->first();

                        if ($counterEntryLedgerDetails) {
                            $ledgerAccName = $counterEntryLedgerDetails->ledger_account_name;
                            unset($counterEntryLedgerDetails);
                        }
                    }

                    $ledgerNameArr[] = [
                        "name" => "By " . $ledgerAccName,
                        "ledger_id" => $counterEntry[0]->ledger_account_id,
                    ];
                } else {
                    $ledgerNameArr[] = [
                        "name" => "For " . $txnList->ledger_account_name,
                        "ledger_id" => $txnList->ledger_account_id,
                    ];
                }
            }
        }
        return $ledgerNameArr;
    }

    public function getBankTransaction($ledgerId, $endDate, $startDate, $customDateFilter = 0, $type = null)
    {
        $finalArray = [];

        if (!empty($customDateFilter)) {
            $key = ($customDateFilter == 1) ? "transaction_date" : "value_date";
        }

        if (isset($type)) {
            if ($type == 2) { //Unreconciled
                $nonRecoTxn = $this->tenantDB()->table("chsone_ledger_transactions AS ledgTxn")
                    ->where("ledger_account_id", $ledgerId)
                    ->where($key, ">=", $startDate)
                    ->where($key, "<=", $endDate)
                    ->where("is_reconciled", "0")
                    ->where("is_opening_balance", "0")
                    ->whereRaw("is_cancelled = 0 OR is_cancelled IS NULL")
                    ->orderBy($key)
                    ->get()->toArray();
                $finalArray = array_merge($nonRecoTxn);
            } elseif ($type == 1) { //Reconciled
                $bankRecoTxn = $this->tenantDB()->table("chsone_ledger_transactions AS ledgTxn")
                    ->where("ledger_account_id", $ledgerId)
                    ->where($key, ">=", $startDate)
                    ->where($key, "<=", $endDate)
                    ->where("is_reconciled", "1")
                    ->where("is_opening_balance", "0")
                    ->whereRaw("is_cancelled = 0 OR is_cancelled IS NULL")
                    ->orderBy($key)
                    ->get()->toArray();
                $finalArray = array_merge($bankRecoTxn);
            } else {
                $key = !empty($customDateFilter) ? $key : 'transaction_date';
                $key2 = !empty($customDateFilter) ? $key : 'value_date';

                $bankRecoTxn = $this->tenantDB()->table("chsone_ledger_transactions AS ledgTxn")
                    ->where("ledger_account_id", $ledgerId)
                    ->where($key2, ">=", $startDate)
                    ->where($key2, "<=", $endDate)
                    ->where("is_reconciled", "1")
                    ->where("is_opening_balance", "0")
                    ->whereRaw("is_cancelled = 0 OR is_cancelled IS NULL")
                    ->orderBy($key2)
                    ->get()->toArray();
                $nonRecoTxn = $this->tenantDB()->table("chsone_ledger_transactions AS ledgTxn")
                    ->where("ledger_account_id", $ledgerId)
                    ->where($key, ">=", $startDate)
                    ->where($key, "<=", $endDate)
                    ->where("is_reconciled", "0")
                    ->where("is_opening_balance", "0")
                    ->whereRaw("is_cancelled = 0 OR is_cancelled IS NULL")
                    ->orderBy($key)
                    ->get()->toArray();
                $finalArray = array_merge($bankRecoTxn, $nonRecoTxn);
            }
        } else {
            $prevNonRecoTxn = $this->tenantDB()->table("chsone_ledger_transactions")
                ->where("ledger_account_id", $ledgerId)
                ->where("transaction_date", "<", $startDate)
                ->where("is_reconciled", "0")
                ->where("is_opening_balance", "0")
                ->whereRaw("is_cancelled = 0 OR is_cancelled IS NULL")
                ->orderBy("transaction_date")
                ->get()->toArray();

            $bankRecoTxn = $this->tenantDB()->table("chsone_ledger_transactions")
                ->where("ledger_account_id", $ledgerId)
                ->where("value_date", ">=", $startDate)
                ->where("value_date", "<=", $endDate)
                ->where("is_reconciled", "1")
                ->where("is_opening_balance", "0")
                ->whereRaw("is_cancelled = 0 OR is_cancelled IS NULL")
                ->orderBy("transaction_date")
                ->get()->toArray();

            $nonRecoTxn = $this->tenantDB()->table("chsone_ledger_transactions")
                ->where("ledger_account_id", $ledgerId)
                ->where("transaction_date", ">=", $startDate)
                ->where("transaction_date", "<=", $endDate)
                ->where("is_reconciled", "0")
                ->where("is_opening_balance", "0")
                ->whereRaw("is_cancelled = 0 OR is_cancelled IS NULL")
                ->orderBy("transaction_date")
                ->get()->toArray();

            $finalArray = array_merge($prevNonRecoTxn, $bankRecoTxn, $nonRecoTxn);
        }
        $ledgerNames = $this->getCounterEntryLedgerName($finalArray);

        foreach ($finalArray as $keys => &$items) {
            $items->from_account = $ledgerNames[$keys]['name'];
        }
        return $finalArray;
    }

    public function getFinancialYearStartDate()
    {
        return $this->tenantDB()->table("soc_account_financial_year_master AS financial_master")
            ->where("confirmed", "0")
            ->orderBy("account_closing_id")
            ->get()->toArray();
    }

    public function getBankLedger($context = "bank")
    {
        return $this->tenantDB()->table("chsone_grp_ledger_tree AS ledgTree")
            ->where("context", $context)
            ->where("entity_type", "ledger")
            ->get()->toArray();
    }

    public function getGroupId($groupName, $context = false)
    {
        $grpLedgTree = $this->tenantDB()->table("chsone_grp_ledger_tree");
        if ($context) {
            $grpLedgTree->whereRaw("LOWER(context) = '$groupName'");
        } else {
            $grpLedgTree->whereRaw("LOWER(ledger_account_name) = '$groupName'");
        }
        $result = $grpLedgTree->first();
        if (!is_null($result)) {
            return $result;
        }
        return false;
    }

    public function getCurrentFinancialYear()
    {
        $financialYear = $this->tenantDB()->table("soc_account_financial_year_master")
            ->where("confirmed", 0)
            ->orderBy("account_closing_id")
            ->first();
        return $financialYear;
    }

    public function updateContextRef($ledgerId, $contextRefId)
    {
        if ($ledgerId != "" && $contextRefId != "") {
            $ledgerId = preg_replace("/[^\D]/", "", $ledgerId);
            $ledger = $this->tenantDB()->table("chsone_grp_ledger_tree")
                ->where("ledger_account_id", $ledgerId);
            $ledgerExist = $ledger->first();
            if (!empty($ledgerExist)) {
                $updateLedger = $ledger->update([
                    "context_ref_id" => $contextRefId
                ]);
                return true;
            }
            return false;
        }
    }

    public function getLedger($ledgerId, $returnObj = 1)
    {
        $ledger = $this->tenantDB()->table("chsone_grp_ledger_tree")
            ->where("ledger_account_id", $ledgerId)
            ->first();
        if ($ledger) {
            if ($returnObj) {
                return $ledger;
            } else {
                return $ledger;
            }
        } else {
            return false;
        }
    }

    public function dataManipulation($ledgerName, $entityType = "group", $grpLedgId = "", $parentGrp = 0, $behavior = "", $ledgerId = "", $openingBal = 0, $updateLedgId = "", $ledgStartDate = "", $ledgerType = "", $context = "", $txnId = 0, $companyId = 0, $isReco = 0)
    {
        $this->constants = Config::get("constants");
        $duplicate = $this->checkDuplicateGrpName($parentGrp, $ledgerName, $ledgerId, $updateLedgId);
        $fyDetail = $this->getCurrentFinancialYear();
        $ledgerStartDate = $fyDetail->fy_start_date;
        if ($duplicate == 0) {
            $behavior = trim(strtolower($behavior));
            $grpLedgTreeObj = new ChsoneGrpLedgerTree;
            if ($ledgerId) {
                $grpLedgTreeObj = $this->getLedger($ledgerId);
            }
            $grpLedgTreeObj->entity_type = $entityType;
            $grpLedgTreeObj->soc_id = $this->input['company_id'];
            $grpLedgTreeObj->ledger_account_name = $ledgerName;
            $grpLedgTreeObj->ledger_start_date = $ledgStartDate;
            $grpLedgTreeObj->context_ref_id = 0;

            if (!empty($ledgerType)) {
                $grpLedgTreeObj->operating_type = $ledgerType;
            } else {
                $grpLedgTreeObj->operating_type = "";
            }
            if (!empty($grpLedgId)) {
                $grpLedgTreeObj->ledger_account_id = $grpLedgId;
            }

            if ($updateLedgId != "") {
                $grpLedgTreeObj->ledger_account_id = $updateLedgId;
            }

            if (!empty($parentGrp)) {
                $grpLedgTreeObj->parent_id = $parentGrp;
                $ledgerProps = $this->getLedgerProps($parentGrp);
            } else {
                $grpLedgTreeObj->parent_id = 0;
            }

            if (!empty($behavior)) {
                $grpLedgTreeObj->behaviour = $behavior;
                $grpLedgTreeObj->nature_of_account = $this->constants['nature_account'][$behavior];
                $grpLedgTreeObj->report_head = $this->constants['report_head'][$behavior];
            } else {
                $grpLedgTreeObj->behaviour = $ledgerProps['behavior'];
                $grpLedgTreeObj->nature_of_account = $ledgerProps['nature_account'];
                $grpLedgTreeObj->report_head = $ledgerProps['report_head'];
            }

            if (!empty($context)) {
                $grpLedgTreeObj->context = $context;
            } else {
                $grpLedgTreeObj->context = $ledgerProps['context'];
            }
            $grpLedgTreeObj->defined_by = $this->constants['USER'];
            $grpLedgTreeObj->status = $this->constants['ACTIVE'];
            $grpLedgTreeObj->added_on = date("Y-m-d H:i:s");
            if ($grpLedgTreeObj->save()) {
                if (!in_array(strtolower($grpLedgTreeObj->behaviour), ["income", "expense"])) {
                    $txnDate = $ledgerStartDate;
                    $narration = "entry for opening balance";

                    if ($txnId = $this->addTxn($grpLedgTreeObj->ledger_account_id, $openingBal, $narration, $txnDate, "", $grpLedgTreeObj->nature_of_account, "", "", "", "", $ledgerName, $isOpening = 1, $isReco)) {
                        //do nothing
                    }
                }
                return $grpLedgTreeObj->ledger_account_id;
            } else {
                return false;
            }
        } else {
            return "DUP" . $duplicate;
        }
    }

    public function checkDuplicateGrpName($parentGrp, $ledgerName, $ledgerId = "", $updateLedgId = "")
    {
        $grpLedgTree = $this->tenantDB()->table("chsone_grp_ledger_tree")
            ->whereRaw("LOWER(ledger_account_name) = '" . strtolower(trim($ledgerName)) . "'");
        if ($ledgerId) {
            $grpLedgTree = $grpLedgTree->where("ledger_account_id", $ledgerId);
        }
        if ($updateLedgId) {
            $grpLedgTree = $grpLedgTree->where("ledger_account_id", $updateLedgId);
        }

        return $grpLedgTree->count();
    }

    public function addTxn($ledgerAccId, $txnAmt, $narration, $txnDate, $voucherType = "journal", $natureOfAcc, $fromTxnId = "", $payMode = "", $payRef = "", $openingBal = "", $ledgerName, $isOpening = 1, $isReco)
    {
        return $this->addTransaction($isReco, $ledgerAccId, $txnAmt, $narration, $txnDate, $voucherType, $natureOfAcc, $fromTxnId, $payMode, $payRef, $ledgerName, $isOpening = 1);
    }

    public function addTransaction($isReco = 0, $ledgerAccId, $txnAmt, $narration, $txnDate, $voucherType = "journal", $natureOfAcc, $fromTxnId, $payMode, $payRef, $ledgerName, $isOpening = "", $otherRecpRef = "", $socId = "", $createdBy = "", $voucherReferenceId = "", $voucherReferenceNumber = "", $isCancelled = 0)
    {
        $txn = new ChsoneLedgerTransaction();
        $mode = "";

        if (!empty($natureOfAcc)) {
            $typeTxn = $natureOfAcc;
        } else {
            if ($fromTxnId) {
                $mode = MODE_TO;
                $typeTxn = "cr";
            } else {
                $mode = MODE_FROM;
                $typeTxn = "dr";
            }
        }
        if ($isOpening == 1) {
            $txnEntries = $txn->where("is_opening_balance", "1")
                ->where("ledger_account_id", $ledgerAccId)
                ->where("transaction_date", $txnDate)
                ->first();
            $txn->value_date = $txnDate;
            $txn->txn_id = (isset($txnEntries->txn_id) && !empty($txnEntries->txn_id)) ? $txnEntries->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = $txn->where('txn_id', $txn->txn_id)->first();
            }
        }
        if ($ledgerName && $voucherType && $mode) {
            $txn->soc_id = $socId;
            $txn->transaction_date = $txnDate;
            $txn->ledger_account_id = $ledgerAccId;
            $txn->ledger_account_name = $ledgerName;
            $txn->voucher_type = $voucherType;
            $txn->transaction_type = $typeTxn; //$ledger_name_obj->nature_of_account;//$txn_type;
            $txn->payment_mode = $payMode;
            $txn->payment_reference = $payRef;
            $txn->transaction_amount = (is_null($txnAmt)) ? $txn->transaction_amount : $txnAmt;
            $txn->other_reference_id = $otherRecpRef;
            $txn->txn_from_id = $fromTxnId;
            $txn->memo_desc = $narration;
            $txn->is_opening_balance = $isOpening;
            $txn->is_reconciled = $isReco;
            $txn->voucher_reference_number = $voucherReferenceNumber;
            $txn->voucher_reference_id = $voucherReferenceId;
            $txn->is_cancelled = $isCancelled;
            $txn->created_by = $createdBy;
            $txn->added_on = date("Y-m-d H:i:s");
            if ($txn->save()) {
                return $txn->txn_id;
            }
            return false;
        } else {
            return false;
        }
    }

    public function getParentGroupId($arrLedgerData)
    {
        $grpLedgTree = $this->tenantDB()->table("chsone_grp_ledger_tree")
            ->where("ledger_account_name", $arrLedgerData['group_name'])
            ->where("behaviour", $arrLedgerData['behaviour'])
            ->where("context", $arrLedgerData['context'])
            ->where("entity_type", "group")
            ->first();
        if (!empty($grpLedgTree)) {
            return $grpLedgTree;
        }
        return false;
    }

    public function getMemberDetail($arrAccData)
    {
        $memberType = $this->tenantDB()->table("chsone_member_type_master")
            ->where("member_type_name", "Primary")
            ->first();
        if (!empty($memberType)) {
            $memberTypeId = $memberType->member_type_id;
            $memberMaster = $this->tenantDB()->table("chsone_members_master")
                ->where("fk_unit_id", $arrAccData['unit_id'])
                ->where("status", "1")
                ->where("member_type_id", $memberTypeId)
                ->first();
            if (!empty($memberMaster)) {
                return $memberMaster;
            }
        }
        return false;
    }

    public function saveCreditAccountResponse($data)
    {
        if (empty($data)) {
            return false;
        }
        $id = $data['id'];

        if ($id) {
            $cr = new ChsoneCreditAccount();
            $crObjExist = $cr->where("credit_account_id", $id)
                ->first();
            $cr->updated_date = $this->getCurrentDate('database');
            $cr->use_credit_after = (!empty($data['adjustable_date']) ? $data['adjustable_date'] : null);
            $cr->is_locked = 0;
            $cr->use_credit_for = (!empty($data['used_for']) ? $data['used_for'] : null);

            $cr->is_invoice_rectification = ((isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification'])) ? $data['is_invoice_rectification'] : null);
            $cr->income_account_id = ((isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id'])) ? $data['income_account_ledger_id'] : null);
            $cr->narration = $data['narration'];
            $cr->use_credit = (isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable');

            if ($cr->save()) {
                return $cr->credit_account_id;
            } else {
                return false;
            }
        } else {
            if ($data['credit_used_type'] == "both") {
                $data['credit_used_type'] = "adjustable";
                $data['payment_amount'] = $data['adjustable_amount'];
                $saveResponse = $this->saveCreditNote($data);
                if ($saveResponse) {
                    return $saveResponse['id'];
                }
            } else {
                $saveResponse = $this->saveCreditNote($data);
                if ($saveResponse) {
                    return $saveResponse['id'];
                }
            }
            return false;
        }
    }

    public function saveCreditNote($data)
    {
        $cr = new ChsoneCreditAccount();
        $cr->created_date = $this->getCurrentDate('database');
        $cr->soc_id = $data['soc_id'];
        $cr->invoice_number = (((isset($data['invoice_no']) && !empty($data['invoice_no'])) ? $data['invoice_no'] : (isset($data['invoice_number']) && !empty($data['invoice_number']))) ? $data['invoice_number'] : null);
        $cr->is_invoice_rectification = ((isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification'])) ? $data['is_invoice_rectification'] : null);
        $cr->income_account_id = ((isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id'])) ? $data['income_account_ledger_id'] : null);
        $cr->payment_tracker_id = (isset($data['payment_tracker_id']) && !empty($data['payment_tracker_id']) ? $data['payment_tracker_id'] : null);
        $cr->account_id = $data['account_id'];
        $cr->account_name = $data['account_name'];
        $cr->account_context = $data['account_context'];
        $cr->amount = $data['payment_amount'];
        $cr->payment_mode = (isset($data['payment_mode']) && !empty($data['payment_mode']) ? $data['payment_mode'] : null);
        $cr->payment_date = (isset($data['payment_date']) && !empty($data['payment_date']) ? $this->getDatabaseDate($data['payment_date']) : null);
        $cr->transaction_type = $data['transaction_type'];
        $cr->narration = $data['narration'];
        $cr->use_credit = (isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable');
        $cr->use_credit_after = (isset($data['adjustable_date']) && !empty($data['adjustable_date']) ? $data['adjustable_date'] : null);
        $cr->is_locked = (isset($data['is_locked']) && !empty($data['is_locked']) ? $data['is_locked'] : 0);
        $cr->use_credit_for = (isset($data['used_for']) && !empty($data['used_for']) ? $data['used_for'] : null);
        $cr->reference_no = (isset($data['transaction_reference']) && !empty($data['transaction_reference']) ? $data['transaction_reference'] : null);
        $cr->context = (isset($data['context']) && !empty($data['context']) ? $data['context'] : 'system');

        if ($cr->save()) {
            return ["id" => $cr->credit_account_id];
        }
        return false;
    }

    public function getLedgerProps($parentGrp)
    {
        $finalArray = [];
        if (is_numeric($parentGrp)) {
            $ledgerProps = ChsoneGrpLedgerTree::where("ledger_account_id", $parentGrp)
                ->selectRaw("
                                              ledger_account_name,
                                              nature_of_account,
                                              behaviour,
                                              report_head,
                                              context")
                ->get()
                ->toArray();
        }
        if (!empty($ledgerProps)) {
            foreach ($ledgerProps as $ledgerProp) {
                $finalArray["ledger_account_name"] = $ledgerProp['ledger_account_name'];
                $finalArray["behaviour"] = $ledgerProp['behaviour'];
                $finalArray["nature_account"] = $ledgerProp['nature_of_account'];
                $finalArray["report_head"] = $ledgerProp['report_head'];
                $finalArray["context"] = $ledgerProp['context'];
            }
        }
        return $finalArray;
    }

    public function getLedgGroupTree($voucherType, $mode, $entityType = "", $ledgerId = "", $socId = 0, $b)
    {
    }

    public function saveVoucherEntry($data)
    {
        $objVoucher = isset($data['voucher_id']) ?
            ChsoneVoucherMaster::where('voucher_id', $data['voucher_id'])
            ->where('soc_id', $data['company_id'])
            ->first() : new ChsoneVoucherMaster();

        $objVoucher->fill([
            'created_date' => $this->getCurrentDate('database'),
            'soc_id' => $data['soc_id'],
            'transaction_date' => $data['transaction_date'] ?? null,
            'type' => $data['voucher_type'] ?? null,
            'sub_type' => $data['type'] ?? null,
            'from_ledger_account_id' => $data['from_ledger_account_id'] ?? null,
            'to_ledger_account_id' => $data['to_ledger_account_id'] ?? null,
            'from_ledger_account_name' => $data['from_ledger_account_name'] ?? null,
            'to_ledger_account_name' => $data['to_ledger_account_name'] ?? null,
            'amount' => $data['amount'],
            'reference' => $data['reference'] ?? null,
            'narration' => $data['narration'] ?? null,
            'status' => $data['status'] ?? 1,
            'updated_date' => $this->getCurrentDate('database')
        ]);
        $finalArray = [];

        if (!$objVoucher->save()) {
            $finalArray['success'] = false;
            $finalArray['message'] = $objVoucher->getErrors()->all();
        } else {
            $finalArray['success'] = true;
            $finalArray['voucher_id'] = $objVoucher->voucher_id;
        }

        return $finalArray;
    }

    public function executeVoucher($data)
    {
        $finalArray = [];
        if (isset($data['voucher_type'])) {
            if ((strpos($data['voucher_type'], '_') !== false) && (strpos($data['voucher_type'], '_') > 0)) {
                $finalArray = explode('_', $data['voucher_type']);
                $data['voucher_type'] = $finalArray[0];
            }
        }
        $txnFromId = $this->addTransaction(0, $data['from_ledger_account_id'], $data['amount'], $data['narration'], $data['transaction_date'], $data['voucher_type'], $data['sub_type'], "", $data['modeOfPayment'], $data['reference'], $data['from_ledger_account_name'], $isOpening = 0, $data['otherRecpRef'], $data['soc_id'], $data['created_by'], $data['voucher_reference_id'], $data['voucher_reference_number'], $data['is_cancelled']);

        if ($txnFromId) {
            if (count($finalArray) >= 2) {
                $data['voucher_type'] = $finalArray[1];
            }
            if ($this->addTransaction(0, $data['to_ledger_account_id'], $data['amount'], $data['narration'], $data['transaction_date'], $data['voucher_type'], $data['sub_type'], $txnFromId , $data['modeOfPayment'], $data['reference'], $data['to_ledger_account_name'], $isOpening = 0, $data['otherRecpRef'], $data['soc_id'], $data['created_by'], $data['voucher_reference_id'], $data['voucher_reference_number'], $data['is_cancelled'])) {
                return $txnFromId;
            }
        }
        return false;
    }

    public function checkLedgerExistNew($arrLedgerData)
    {
        $socId = $this->input['company_id'];
        $arrClientLedgerDetails = [];
        $grpLedgTreeObj = new ChsoneGrpLedgerTree();

        $grpLedgTreeObj = $grpLedgTreeObj->where("entity_type", "ledger");
        if (isset($arrLedgerData['ledger_name']) && !empty($arrLedgerData['ledger_name'])) {
            $grpLedgTreeObj = $grpLedgTreeObj->where("ledger_account_name", $arrLedgerData['ledger_name'])
                ->where("context", $arrLedgerData['context']);
            if (isset($arrLedgerData['behaviour']) && !empty($arrLedgerData['behaviour'])) {
                $grpLedgTreeObj = $grpLedgTreeObj->where("behaviour", $arrLedgerData['behaviour']);
            }
        } elseif (isset($arrLedgerData['ledger_id']) && !empty($arrLedgerData['ledger_id'])) {
            $grpLedgTreeObj = $grpLedgTreeObj->where("ledger_account_id", $arrLedgerData['ledger_id']);
        }

        $grpLedgTreeData = $grpLedgTreeObj->first();

        if (!empty($grpLedgTreeData)) {
            $arrClientLedgerDetails['receiving_ledger_id'] = $grpLedgTreeData->ledger_account_id;
            $arrClientLedgerDetails['receiver_name'] = $grpLedgTreeData->ledger_account_name;
        } elseif (!empty($arrLedgerData['group_name'])) {
            $name = $arrLedgerData['ledger_name'];
            $entityType = "ledger";
            $grpLedgId = "";
            $parent = $this->arrLedgerData($arrLedgerData, TRUE);

            if (empty($parent->ledger_account_id)) {
                return $arrClientLedgerDetails;
            }
            $parentGroup = $parent->ledger_account_id;

            $ledgerId = $this->dataManipulation($name, $entityType, $grpLedgId, $parentGroup, "", "", 0, "", "", "", "", "", 0, $socId);

            if ($ledgerId) {
                $arrClientLedgerDetails['receiving_ledger_id'] = $ledgerId;
                $arrClientLedgerDetails['receiver_name'] = $arrLedgerData['ledger_name'];
            }
        }
        unset($grpLedgTreeData);
        return $arrClientLedgerDetails;
    }

    public function arrLedgerData($arrData)
    {
        $parent = ChsoneGrpLedgerTree::where('soc_id', $this->input['company_id'])
            ->where('ledger_account_name', $arrData['group_name'])
            ->where('entity_type', ENTITY_TYPE_GROUP)
            ->where('behaviour', $arrData['behaviour'])
            ->where('context', $arrData['context'])
            ->first();

        if ($parent) {
            return $parent;
        } else {
            return false;
        }
    }

    public function getGeneralSettings($socId)
    {
        $finalData = $this->getAllSettings($socId);
        $settings = $this->tenantDB()->table("income_invoice_settings")
            ->where('soc_id', $socId)
            ->orderByDesc('effective_date')
            ->first();

        $finalData['generalSettingId'] = $settings->id;
        $finalData['invoiceFrequency'] = (isset($settings->invoicing_frequency) &&
            $settings->invoicing_frequency != '') ? $settings->invoicing_frequency : '';
        $finalData['effectiveDate'] = $settings->effective_date;
        $finalData['generalSettingKeyIds'] = $this->getAllSettingsWithIds($socId);
        return $finalData;
    }

    public function getAllSettingsWithIds($socId)
    {
        $data = [];
        $settingsWithIds = $this->tenantDB()->table("income_invoice_general_settings")
            ->where('soc_id', $socId)
            ->get()->toArray();
        foreach ($settingsWithIds as $settingsWithId) {
            $data[$settingsWithId->setting_key] = $settingsWithId->id;
        }
        return $data;
    }

    public function getAllSettings($socId)
    {
        $data = [];
        $allSettings = $this->tenantDB()->table("income_invoice_general_settings")
            ->where('soc_id', $socId)
            ->get()->toArray();
        foreach ($allSettings as $allSetting) {
            $data[$allSetting->setting_key] = $allSetting->setting_value;
        }
        return $data;
    }

    public function getBankCashAccountDetail()
    {
        $accountDetails = $this->tenantDB()->table("chsone_grp_ledger_tree AS ledgTree")
            ->join(
                "chsone_accounts_master AS accounts",
                "ledgTree. ledger_account_id",
                "=",
                "accounts.ledger_account_id"
            )
            ->where("ledgTree.entity_type", "ledger")
            ->where("ledgTree.status", "1")
            ->whereIn("ledgTree.context", ["cash", "bank"])
            ->select(
                'ledgTree.ledger_account_id',
                'ledgTree.ledger_account_name',
                'ledgTree.context',
                'accounts.account_id',
                'accounts.default_bank_for_incidental',
                'accounts.default_bank_for_nonmember',
                'accounts.default_account',
                'accounts.bank_name',
                'accounts.account_number',
                'accounts.bank_address',
                'accounts.bank_city',
                'accounts.bank_ifsc'
            )
            ->get()->toArray();
        return $accountDetails;
    }

    public function getBankLedgerAccountDetail($accountDetails, $data = [])
    {
        $ledgerAccountDetail = [
            "cash" => [],
            "bank" => [],
            "arrBank" => [],
        ];
        if (!empty($accountDetails)) {
            foreach ($accountDetails as $accountDetail) {
                if (empty($ledgerAccountDetail['cash']) && strtolower($accountDetail['context'] == "cash")) {
                    $ledgerAccountDetail['cash']['ledger_id'] = $accountDetail['ledger_account_id'];
                    $ledgerAccountDetail['cash']['ledger_name'] = $accountDetail['ledger_account_name'];
                    $ledgerAccountDetail['arrCash'][$accountDetail['ledger_account_id']] = $accountDetail['ledger_account_name'];
                } elseif (strtolower($accountDetail['context'] == "bank")) {
                    if ($accountDetail['default_account'] == 1) {
                        $ledgerAccountDetail['bank']['ledger_id'] = $accountDetail['ledger_account_id'];
                        $ledgerAccountDetail['bank']['ledger_name'] = $accountDetail['ledger_account_name'];
                    }
                    $ledgerAccountDetail['arrBank'][$accountDetail['ledger_account_id']] = $accountDetail['ledger_account_name'];
                }
                if ($data['default_bank_incidental'] && $accountDetail['default_bank_for_incidental'] == 1) {
                    $ledgerAccountDetail['bank']['ledger_id'] = $accountDetail['ledger_account_id'];
                    $ledgerAccountDetail['bank']['ledger_name'] = $accountDetail['ledger_account_name'];
                }
                if ($data['default_bank_nonmember'] && $accountDetail['default_bank_for_nonmember'] == 1) {
                    $ledgerAccountDetail['bank']['ledger_id'] = $accountDetail['ledger_account_id'];
                    $ledgerAccountDetail['bank']['ledger_name'] = $accountDetail['ledger_account_name'];
                }
            }
            if (empty($ledgerAccountDetail['bank'])) {
                foreach ($ledgerAccountDetail['arrBank'] as $key => $value) {
                    $ledgerAccountDetail['bank']['ledger_id'] = $key;
                    $ledgerAccountDetail['bank']['ledger_name'] = $value;
                    break;
                }
            }
        }
        return $ledgerAccountDetail;
    }

    public function getUnitStartDate($socId)
    {
        $finalArray = [];
        $accStartMaster = $this->tenantDB()->table("soc_account_start_master")
            ->where("soc_id", $socId)
            ->first();
        $accFinancialMaster = $this->tenantDB()->table("soc_account_financial_year_master")
            ->where("soc_id", $socId)
            ->get()->toArray();
        $finalArray['arraccountStartMaster'] = $accStartMaster;
        $finalArray['arrAccountFinancialMaster'] = $accFinancialMaster;
        return $finalArray;
    }

    public function getLedgGroupTreeOptimize($filter = [], $voucherType, $mode, $entityType = "", $ledgerId = "", $socId = 0, $behavior = "")
    {
        $conditionArr = $this->contextVoucherType($voucherType, $mode);

        if ($entityType == "group") {
            unset($conditionArr['group']);
        }

        $conditions = $conditionsNot = $conditionsGrp = "";

        if (is_array($conditionArr['context'])) {
            $conditions = " context IN ('" . implode("','", $conditionArr['context']) . "') ";
        }

        if (is_array($conditionArr['context_not'])) {
            $conditions = " context NOT IN ('" . implode("','", $conditionArr['context_not']) . "') ";
        }

        if (!empty($ledgerId)) {
            $conditionsGrp = "AND ledger_account_id IN (" . implode(",", $ledgerId) . ")";
        } elseif (is_array($conditionArr['group'])) {
            $conditionsGrp = "AND ledger_account_id IN (" . implode(",", $conditionArr['group']) . ")";
        }

        $findCondition = "";

        if (!empty($conditions) && !empty($conditionsNot)) {
            $findCondition = "((" . $conditions . ") AND (" . $conditionsNot . ")) ";
        } elseif (!empty($conditions)) {
            $findCondition = "(" . $conditions . ") ";
        } elseif (!empty($conditionsNot)) {
            $findCondition = "(" . $conditionsNot . ")";
        }

        if (!empty($conditionsGrp) && !empty($findCondition)) {
            $findCondition .= "OR (" . str_replace('AND', '', $conditionsGrp) . ")";
        } elseif (empty($findCondition) && !empty($conditionsGrp)) {
            $findCondition = " (" . $conditionsGrp . ") ";
        }

        $findCondition .= empty($findCondition) ? " status = '1'" : " AND status = '1'";

        if ($entityType == "bank" || $entityType == "cash" || $entityType == "group") {
            $findCondition .= "AND (behaviour = 'asset' OR behaviour = 'liability')
                               AND entity_type != '" . trim(strtolower("ledger")) . "'";
        }

        $grpLedgerModel = new ChsoneGrpLedgerTree();

        if (!empty($ledgerId)) {
            return $this->$grpLedgerModel->getLedgerTreeByIdsOptimize($ledgerId)->toArray();
        }

        if ((!empty($conditionArr['group'])) && (empty($conditionArr['context'])) && (empty($conditionArr['context_not']))) {
            $groupLedgers = count($conditionArr['group']) == 1 ?
                $grpLedgerModel->getLedgerTreeByIdsOptimize($conditionArr['group'][0], $filter) :
                $grpLedgerModel->getLedgerTreeByIdsOptimize($conditionArr['group'], $filter);
        } else {
            $findCondition = ($behavior = '') ? $findCondition : 'behaviour = "' . $behavior . '" AND status = 1 AND soc_id = ' . $socId;
            $groupLedgers = $grpLedgerModel->getLedgerTreeByConditionOptimize($findCondition, $filter);
        }
        return $this->prepareTreeOptimize($groupLedgers->toArray(), 0, '', $filter['report_type']);
    }

    public function contextVoucherType($voucherType, $mode)
    {
        $this->constants = Config::get("constants");
        $context = "";
        $contextNot = "";
        if ($mode == "from") {
            $voucherType = "payment";
            if (isset($this->constants['context_from_conf_arr'][$voucherType])) {
                $context = $this->constants['context_from_conf_arr'][$voucherType];
            }
            if (isset($this->constants['context_from_not_in_conf_arr'][$voucherType])) {
                $contextNot = $this->constants['context_from_not_in_conf_arr'][$voucherType];
            }
            if (isset($this->constants['context_from_grp_array'][$voucherType])) {
                $strings = $this->constants['context_from_grp_array'][$voucherType];
            }
        }

        if ($mode == "to") {
            if (isset($this->constants['context_to_conf_arr'][$voucherType])) {
                $context = $this->constants['context_to_conf_arr'][$voucherType];
            }
            if (isset($this->constants['context_to_not_in_conf_arr'][$voucherType])) {
                $contextNot = $this->constants['context_to_not_in_conf_arr'][$voucherType];
            }
            if (isset($this->constants['context_to_grp_array'][$voucherType])) {
                $strings = $this->constants['context_to_grp_array'][$voucherType];
            }
        }
        $groups = $this->tenantDB()->table("chsone_grp_ledger_tree")
            ->whereIn("ledger_account_name", $strings)
            ->where("soc_id", $this->input['company_id'])
            ->select("ledger_account_id")
            ->get()->toArray();

        if (!empty($groups)) {
            foreach ($groups as $group) {
                $grpId[] = $group->ledger_account_id;
            }
        } else {
            $grpId = [];
        }
        return [
            "context" => $context,
            "context_not" => $contextNot,
            "group" => $grpId
        ];
    }

    public function prepareTreeOptimize($tree, $root = 0, $context = "", $report_type = "")
    {
        $grandNew   = 0;
        $balanceNew = 0;
        $crNew      = 0;
        $drNew      = 0;
        foreach ($tree as $key => $parent) {
            if ($parent['parent'] == $root) {
                unset($tree[$key]);
                $newArray = [];
                $newArray['name'] = $parent['ledger_account_name'];
                $newArray['nature'] = $parent['nature_of_account'];
                $newArray['behaviour'] = $parent['behaviour'];
                $newArray['entity_type'] = $parent['entity_type'];
                $newArray['ledger_account_id'] = $parent['ledger_account_id'];
                $newArray['parent'] = $parent['parent'];
                $newArray['status'] = $parent['status'];
                $parent['context'] = $newArray['context'] = (trim($parent['context']) != "" ?
                    preg_replace("/[0-9]+/", "", $parent['context']) : $context);
                $newArray['is_parent'] = "yes";
                $newArray['operating_type'] = $parent["operating_type"];

                if ($newArray["entity_type"] == 'ledger') {
                    $ledgersCR = $parent['crbal'];
                    $ledgersDR = $parent['drbal'];
                    $ledgerCrPre = $parent['crbalpre'];
                    $ledgerDrPre = $parent['drbalpre'];
                    $transaction_amount = $parent['openingbal'];
                    $transaction_type  = $parent['openingbaltype'];
                    $newArray['ledger_added'] =
                        ($newArray['nature'] == 'cr') ? ($ledgersCR) - ($ledgersDR) : ($ledgersDR) - ($ledgersCR);
                    if (($newArray["behaviour"] == "expense" || $newArray["behaviour"] == "income") && !empty($report_type) && $report_type == "trial_balance") {
                        $newArray['ledger_balance_pre'] = 0.00;
                    } else {
                        $newArray['ledger_balance_pre'] =
                            ($newArray['nature'] == 'cr') ? ($ledgerCrPre) - ($ledgerDrPre) : ($ledgerDrPre) - ($ledgerCrPre);
                    }
                    $newArray['ledger_balance_pre'] =
                        ($newArray['nature'] == $parent['openingbaltypepre']) ? ($newArray['ledger_balance_pre'] +  $parent['openingbalpre']) : ($newArray['ledger_balance_pre'] - $parent['openingbalpre']);
                    $newArray['opening_balance'] = ($transaction_amount) ? $transaction_amount : 0.00;
                    $newArray['opening_balance'] = ($transaction_type == $newArray['nature']) ? number_format($newArray['opening_balance'], 3, '.', '') : number_format(($newArray['opening_balance'] * -1), 3, '.', '');
                    $crNew += $newArray['crbal'] = ($ledgersCR) ? number_format($ledgersCR, 3, '.', '') : 0;
                    $drNew += $newArray['drbal'] = ($ledgersDR) ? number_format($ledgersDR, 3, '.', '') : 0;


                    $balanceNew += $newArray['ledger_balance_pre_show'] = ($newArray['opening_balance'] != '0.00') ? $newArray['opening_balance'] : $newArray['ledger_balance_pre'];
                    $grandNew += $newArray['ledger_balance'] = number_format($newArray['ledger_balance_pre_show'] + $newArray['ledger_added'], 3, '.', '');
                }

                $return[$parent['ledger_account_id']] = $newArray;
                $child = $this->prepareTreeOptimize($tree, $parent['ledger_account_id'], $newArray["context"], $report_type);
                if (empty($child)) {
                    $return[$parent['ledger_account_id']]['is_parent'] = 'no';
                }
                $return[$parent['ledger_account_id']]['children'] = $child['children'];

                if ($newArray["entity_type"] != 'ledger') {
                    if (!empty($return[$parent['ledger_account_id']]['children'])) {
                        $temp = [];
                        foreach ($return[$parent['ledger_account_id']]['children'] as $c => $childd) {
                            if ($childd["entity_type"] == 'ledger') {
                                $temp[$c] = $childd;
                                unset($return[$parent['ledger_account_id']]['children'][$c]);
                            }
                        }
                        $return[$parent['ledger_account_id']]['children'] = ($temp + $return[$parent['ledger_account_id']]['children']);
                    }
                    $grandNew += $return[$parent['ledger_account_id']]['final'] = $child['grand'];
                    $balanceNew += $return[$parent['ledger_account_id']]['bal'] = $child['bal'];
                    $crNew += $return[$parent['ledger_account_id']]['crTotal'] = $child['crTotal'];
                    $drNew += $return[$parent['ledger_account_id']]['drTotal'] = $child['drTotal'];
                    $return[$parent['ledger_account_id']]['opening'] = $balanceNew;
                }
            } else {
                continue;
            }
            $parent["context"] = $context = (trim($parent["context"]) != "") ? $parent["context"] : $context;
        }
        return empty($return) ? null : array("children" => $return, 'grand' => $grandNew, 'bal' => $balanceNew, 'crTotal' => $crNew, 'drTotal' => $drNew);
    }

    public function getLedgers($id, $returnObj = 1, $conditions)
    {
        $queryConditions = "";

        $ledgerAccIds = is_array($id) ? $id : [$id];
        $ledgers = [];

        $groups = $this->tenantDB()->table("chsone_grp_ledger_tree")
            ->whereIn("parent_id", $ledgerAccIds)
            ->where("entity_type", "<>", "ledger")
            ->pluck("ledger_account_id")
            ->all();

        if (!empty($groups)) {
            $ledgers = $this->getLedgers($groups, $returnObj, $conditions);
        } else {
            $query = $this->tenantDB()->table("chsone_grp_ledger_tree")
                ->whereIn("parent_id", $ledgerAccIds)
                ->where("entity_type", "ledger");
            if (!empty($conditions['nature_of_account'])) {
                $query->where("nature_of_account", $conditions['nature_of_account']);
            }

            $ledgerData = $query->get([
                'ledger_account_name as name',
                'nature_of_account as nature',
                'behaviour',
                'entity_type',
                'ledger_account_id',
                'parent_id as parent',
                'status',
                'context',
                'operating_type'
            ]);

            $ledgers = $ledgerData->toArray();
        }
        return empty($ledgers) ? false : $ledgers;
    }

    public function manageTree(&$tree, $params = [])
    {
        if (empty($params)) {
            return ['grand' => 0, 'bal' => 0];
        }
        extract($params);
        $grandNew = 0;
        $balanceNew = 0;

        foreach ($tree as $key => &$branch) {
            if ($branch->entity_type === "ledger") {
                $ledgerData = $this->fetchLedgerData($branch->ledger_account_id, $soc_id, $start_date, $end_date, $start_date_pre, $end_date_pre);
                $this->calculateLedgerBalances($branch, $ledgerData, $params);
                $balanceNew += $branch->ledger_balance_pre_show;
                $grandNew += $branch->ledger_balance;
            } elseif (!empty($branch->children)) {
                $results = $this->manageTree($branch->children, $params);
                $grandNew += $branch->final = $results['grand'];
                $balanceNew += $branch->bal = $results['bal'];
                $branch->opening = $balanceNew;
            }
        }
        return ['grand' => $grandNew, 'bal' => $balanceNew, 'crbal' => $branch->crbal, 'drbal' => $branch->drbal];
    }

    private function calculateLedgerBalances(&$branch, $ledgerData, $params)
    {
        // Extract the necessary parameters
        $nature = $branch->nature ?? 'cr';  // Default to 'cr' if nature is not specified

        // Calculate balances for the current period
        $ledgersCR = $ledgerData->crbal ?? 0;
        $ledgersDR = $ledgerData->drbal ?? 0;
        $branch->ledger_added = ($nature === 'cr') ? ($ledgersCR - $ledgersDR) : ($ledgersDR - $ledgersCR);

        // Calculate balances for the previous period
        $ledgerCrPre = $ledgerData->crbalpre ?? 0;
        $ledgerDrPre = $ledgerData->drbalpre ?? 0;
        $branch->ledger_balance_pre = ($nature === 'cr') ? ($ledgerCrPre - $ledgerDrPre) : ($ledgerDrPre - $ledgerCrPre);

        // Handle opening balance and its type
        $openingBalance = $ledgerData->openingbal ?? 0;
        $openingBalanceType = $ledgerData->openingbaltype ?? $nature; // Assume nature if no opening balance type is set

        // Adjust opening balance based on its type
        $branch->opening_balance = $openingBalance;
        $branch->opening_balance = ($openingBalanceType === $nature) ?
            number_format($openingBalance, 2, '.', '') :
            number_format(-$openingBalance, 2, '.', '');

        // Combine current and opening balances
        $branch->ledger_balance_pre_show = ($branch->opening_balance !== '0.00') ?
            $branch->opening_balance :
            $branch->ledger_balance_pre;

        // Calculate final balance for display
        $branch->ledger_balance = number_format($branch->ledger_balance_pre_show + $branch->ledger_added, 2, '.', '');

        // Format for consistency and readability
        $branch->crbal = number_format($ledgersCR, 2, '.', '');
        $branch->drbal = number_format($ledgersDR, 2, '.', '');
    }

    public function fetchLedgerData($ledgerId, $socId, $startDate, $endDate, $startDatePre, $endDatePre)
    {
        return ChsoneLedgerTransaction::where("ledger_account_id", $ledgerId)
            ->where("soc_id", $socId)
            ->select(DB::raw("
                                        SUM(CASE WHEN transaction_date BETWEEN '{$startDate}' AND '{$endDate}' AND transaction_type = 'dr' THEN transaction_amount ELSE 0 END) AS drbal,
                                        SUM(CASE WHEN transaction_date BETWEEN '{$startDatePre}' AND '{$endDatePre}' AND transaction_type = 'dr' THEN transaction_amount ELSE 0 END) AS drbalpre,
                                        SUM(CASE WHEN transaction_date BETWEEN '{$startDate}' AND '{$endDate}' AND transaction_type = 'cr' THEN transaction_amount ELSE 0 END) AS crbal,
                                        SUM(CASE WHEN transaction_date BETWEEN '{$startDatePre}' AND '{$endDatePre}' AND transaction_type = 'cr' THEN transaction_amount ELSE 0 END) AS crbalpre,
                                        MAX(CASE WHEN is_opening_balance != 0 THEN transaction_amount ELSE 0 END) AS openingbal,
                                        MAX(CASE WHEN is_opening_balance != 0 THEN transaction_type ELSE NULL END) AS openingbaltype
                                      "))->first();
    }

    public function calMonthlyPeriod($data)
    {
        $intfystart = $data['fystart'] ?? null;
        $lstYear = $data['lstYear'] ?? null;
        $lstMonth = $data['lstMonth'] ?? 0;
        $uptoMonth = $data['uptoMonth'] ?? 3;
        $endMonthYear = '';

        // Calculate start / end month-year
        $arryears = explode("-", $lstYear);
        $endYear = ($lstMonth < $intfystart) ? $arryears[1] : $arryears[0];

        if ($lstYear == $data['lastyearstart']) {
            $endMonth = ($lstMonth > 0) ? $lstMonth : date("m");
            $endMonthYear = $endYear . "-" . $endMonth;

            if ($uptoMonth == 0) {
                $upto = (date("m") < $intfystart) ? date("m") + 12 : date("m");
                $uptoMonth = $upto - $intfystart;
            }
            $startMonthYear = date("Y-m", strtotime("-$uptoMonth months", strtotime($endMonthYear . "-01")));
        } else {
            $endMonth = ($lstMonth > 0) ? str_pad($lstMonth, 2, '0', STR_PAD_LEFT) : str_pad($intfystart, 2, '0', STR_PAD_LEFT);
            $endMonth = ($endMonth != '01') ? date('m', strtotime($endYear . "-" . $endMonth . "-01 -1 day")) : '01';
            $endMonthYear = $endYear . "-" . $endMonth;

            if ($uptoMonth == 0) {
                $upto = $lstMonth - 12;
                $uptoMonth = $upto - $intfystart;
            }
            $startMonthYear = date("Y-m", strtotime("-$uptoMonth months", strtotime($endMonthYear . "-01")));
        }

        // Create array for monthly period
        $start_month = [];
        for ($i = 0; $i <= $uptoMonth; $i++) {
            $nextMonth = date("Y-m", strtotime("+$i months", strtotime($startMonthYear . "-01")));
            if ($nextMonth <= $endMonthYear) {
                $start_month[] = $nextMonth;
            }
        }
        return $start_month;
    }

    public function calYearlyPeriod($data)
    {
        $lstYear = $data['lstYear'] ?? date("Y");
        $uptoYear = ($data['uptoYear'] && $data['uptoYear'] !== 0 && !empty($data['uptoYear'])) ? $data['uptoYear'] : 1;

        $arrYears = explode("-", $lstYear);
        $currentFyStartYear = $arrYears[0] . "-" . $data['fyyears']['arraccountStartMaster']->fy_start_from;

        $start_month = [];

        for ($i = $uptoYear - 1; $i >= 0; $i--) {
            $fyStartYear = date("Y", strtotime("$currentFyStartYear -$i year"));
            $fyStartDate = date("Y-m-d", strtotime("$currentFyStartYear -$i years"));
            $fyEndDate = date("Y-m-d", strtotime("$fyStartDate +1 year -1 day"));

            if ($data['fyyears']['arraccountStartMaster']->fy_start_from == '01-01') {
                $start_month[$fyStartYear] = $fyStartYear;
            } else {
                $fyEndYear = date("Y", strtotime($fyEndDate));
                $fyYear = $fyStartYear . "-" . $fyEndYear;
                $start_month[$fyYear] = "$fyStartDate-to-$fyEndDate";
            }
        }
        return $start_month;
    }

    public function getCurrentActiveFinancialYear()
    {
        return $this->tenantDB()->table("soc_account_financial_year_master AS financial_master")
            ->where("confirmed", "0")
            ->where("closed", "0")
            ->orderBy("account_closing_id")
            ->first();
    }

    public function getCurrentActiveFinancialYearForBillingPeriod()
    {
        return $this->tenantDB()->table("soc_account_financial_year_master AS financial_master")
            ->where("confirmed", "0")
            ->orderBy("account_closing_id")
            ->get();
    }

    public function download($arrData = [])
    {
        $status = $message = $statusCode = "";
        if (!$arrData['type'] || $arrData['type'] == '' || $arrData['type'] == ':type') {
            $status = 'error';
            $message = 'Please provide a valid type either excel or pdf';
            $statusCode = 400;
            $data = [];
        } else {
            $data = $this->action($arrData['action'], $arrData['pointer'], $arrData['input']);
            $summaryHeadings = $arrData['summary_keys'];
            $header = $arrData['header'];
            $collection = collect($data);
            if (isset($arrData['unset_keys']) && is_array($arrData['unset_keys'])) {
                $unsetKeys = $arrData['unset_keys'];
                $collection = $collection->map(function ($item) use ($unsetKeys) {
                    foreach ($unsetKeys as $key) {
                        unset($item[$key]);
                    }
                    return $item;
                });
            }
            $finalData = [];
            if ($arrData['type'] == 'excel') {
                $response = $this->hitCURLForGenerateCSV($collection, $arrData['headings'], $arrData['file_name'], $header, $summaryHeadings);
                $finalData['url'] = $response['data'];
            } else {
                $response = $this->hitCURLForGeneratePDF($collection, $arrData['headings'], $arrData['file_name'], $header, $summaryHeadings);
                $finalData['url'] = $response['data'];
            }
        }
        $array = [
            "status" => $status,
            "message" => $message,
            "statusCode" => $statusCode,
            "data" => $finalData['url'],
        ];
        return $array;
    }

    public function getLedgerTabs($soc_id){
        $result = $this->tenantDB()->table('chsone_grp_ledger_tree')
        ->where(function ($query) {
            $query->where('nature_of_account', 'dr')
                  ->orWhere('nature_of_account', 'cr')
                  ->orWhere('nature_of_account', 'Credit');
        })
        ->where('soc_id', $soc_id)
        ->where(function ($query) {
            $query->where('parent_id', 0)
                  ->orWhereNull('parent_id');
        })
        ->get();

        return $result;

    }

  
    function validateInput( $input)
    {
        $response = ['status' => 'success', 'message' => 'Validation passed','statusCode' => 200];
    
        try {
            $financial_year = $input['financial_year'] ?? null;
            $type = $input['type'] ?? null;
            $month = $input['month'] ?? null;
            $asondateval = $input['asondateval'] ?? null;
            $soc_id = $input['soc_id'] ?? null;
    
            if ($financial_year) {
                $yearData = explode('-', $financial_year);
    
                if (count($yearData) !== 2) {
                    $response['status'] = 'error';
                    $response['message'] = 'Invalid Year Format. It should be in YYYY-YYYY format.';
                    $response['statusCode'] = 400;
                    return $response;
                    // throw new Exception("Invalid Year Format. It should be in YYYY-YYYY format.");
                }
    
                $start_year = intval($yearData[0]);
                $end_year = intval($yearData[1]);
                $currentYear = intval(date('Y'));
    
                if (strlen($yearData[0]) !== 4 || strlen($yearData[1]) !== 4) {
                    $response['status'] = 'error';
                    $response['message'] = 'Year should be in YYYY format.';
                    $response['statusCode'] = 400;
                    return $response;
                    // throw new Exception("Year should bd de in YYYY format.");
                }
    
                if ($end_year !== $start_year + 1) {
                    $response['status'] = 'error';
                    $response['message'] = 'End year must be exactly one year after the start year.';
                    $response['statusCode'] = 400;
                    return $response;
                    // throw new Exception("End year must be exactly one year after the start year.");
                }
    
                if ($start_year > $currentYear) {
                    $response['status'] = 'error';
                    $response['message'] = 'Start year cannot be greater than the current year.';
                    $response['statusCode'] = 400;
                    return $response;
                    // throw new Exception("Start year cannot be greater than the current year.");
                }
    
                // Fetch financial year start date from DB
                $financialYearRecord = $this->tenantDB()->table('soc_account_financial_year_master')
                    ->where('soc_id', $soc_id)
                    ->orderBy('fy_start_date', 'asc')
                    ->first();
    
                if (!$financialYearRecord) {
                    $response['status'] ='error';
                    $response['message'] = 'Financial year master data not found.';
                    $response['statusCode'] = 400;
                    return $response;
                    // throw new Exception("Financial year master data not found.");
                }
    
                $fy_start_date = $financialYearRecord->fy_start_date;
                $fy_start_year = intval(date('Y', strtotime($fy_start_date)));
    
                if ($start_year < $fy_start_year) {
                    $response['status'] = 'error';
                    $response['message'] = 'Start year cannot be less than the financial year start date.';
                    $response['statusCode'] = 400;
                    return $response;
                    // throw new Exception("Start year cannot be less than the financial year start date ($fy_start_year).");
                }
    
                if ($start_year === $currentYear) {
                    $today = date('Y-m-d');
                    $currentMonth = intval(date('m', strtotime($today)));
    
                    if ($month >= $currentMonth) {
                        $response['status'] = 'error';
                        $response['message'] = 'You can only select months up to ' . date('F', mktime(0, 0, 0, $currentMonth - 1, 1)) . ' for the current financial year '.$start_year."-".$end_year;
                        $response['statusCode'] = 400;
                        return $response;
                        // throw new Exception(
                        //     "You can only select months up to " .
                        //     date('F', mktime(0, 0, 0, $currentMonth - 1, 1)) .
                        //     " for the current financial year ($start_year-$end_year)."
                        // );
                    }
                }
            }
    
            if ($type) {
                if (!in_array($type, ["asonmonth", "yearly", "monthly", "asondate"])) {
                    $response["status"] = "error";
                    $response["message"] = "Invalid type. It should be either 'asonmonth', 'yearly', 'monthly', or 'asondate'.";
                    $response["statusCode"] = 400;
                    return $response;
                    // throw new Exception("Invalid type. It should be either 'asonmonth', 'yearly', 'monthly', or 'asondate'.");
                }
    
                if (in_array($type, ["asonmonth", "monthly"])) {
                    if (!$month) {
                        $response["status"] = "error";
                        $response["message"] = "Month is required for 'asonmonth' or 'monthly' type.";
                        $response["statusCode"] = 400;
                        return $response;
                        // throw new Exception("Month is required for 'asonmonth' or 'monthly' type.");
                    }
    
                    if (!is_numeric($month) || $month < 1 || $month > 12) {
                        $response["status"] = "error";
                        $response["message"] = "Invalid Month. It should be between 1 and 12.";
                        $response["statusCode"] = 400;
                        return $response;
                        // throw new Exception("Invalid Month. It should be between 1 and 12.");
                    }
                }
            }
    
            if ($asondateval) {
                $asondateval = trim($asondateval);
    
                if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $asondateval)) {
                    $response['status'] = 'error';
                    $response['message'] = 'Invalid date format. Expected format is YYYY-MM-DD.';
                    $response['statusCode'] = 400;
                    return $response;
                    // throw new Exception('Invalid date format. Expected format is YYYY-MM-DD.');
                }
    
                $parsedDate = Carbon::createFromFormat('Y-m-d', $asondateval);
    
                if ($parsedDate->format('Y-m-d') !== $asondateval) {
                    $response['status'] = 'error';
                    $response['message'] = 'Invalid date format. Expected format is YYYY-MM-DD.';
                    $response['statusCode'] = 400;
                    return $response;
                    // throw new Exception('Invalid date format. Expected format is YYYY-MM-DD.');
                }
    
                if ($parsedDate->isFuture()) {
                    $response['status'] = 'error';
                    $response['message'] = 'Invalid date. Date cannot be in the future.';
                    $response['statusCode'] = 400;
                    return $response;
                    // throw new Exception('Invalid date. Date cannot be in the future.');
                }
            }
        } catch (\Exception $e) {
            $response['status'] = 'error';
            $response['message'] = $e->getMessage();
            $response['status_code'] = 400;
        }
    
        return $response;
    }
}
    